"""
API配置
"""

import os
from typing import Dict, Any

class APIConfig:
    """API配置类"""
    
    # API版本常量
    API_VERSION = "/v1"
    
    def __init__(self):
        # 🎯 强制默认使用服务端API模式，确保管理端始终使用服务端数据
        self.use_api = True  # 强制启用API模式
        self.api_base_url = os.getenv('API_BASE_URL', 'http://localhost:8000')
        self.api_timeout = int(os.getenv('API_TIMEOUT', '30'))

        # 从配置文件读取设置（如果存在）
        self._load_from_config_file()
    
    def get_config(self) -> Dict[str, Any]:
        """获取API配置"""
        return {
            'use_api': self.use_api,
            'base_url': self.api_base_url,
            'timeout': self.api_timeout
        }
    
    def _load_from_config_file(self):
        """从配置文件加载设置"""
        try:
            import configparser
            config = configparser.ConfigParser()
            config_file = 'config.ini'

            if os.path.exists(config_file):
                config.read(config_file, encoding='utf-8')
                if config.has_section('api'):
                    # 只有明确设置为true时才启用API模式，确保默认使用服务端
                    api_setting = config.get('api', 'use_api', fallback='true').lower()
                    self.use_api = api_setting == 'true'

                    # 更新其他API设置
                    self.api_base_url = config.get('api', 'base_url', fallback=self.api_base_url)
                    self.api_timeout = int(config.get('api', 'timeout', fallback=str(self.api_timeout)))
        except Exception as e:
            print(f"⚠️ 配置文件读取失败，使用默认API模式: {e}")
            self.use_api = True  # 确保默认使用API模式

    def set_api_mode(self, enabled: bool):
        """设置API模式 - 🎯 强制保持API模式，防止意外切换到本地"""
        if not enabled:
            print("⚠️ 警告：尝试禁用API模式，但管理端必须使用服务端数据")
            print("🔒 强制保持API模式以确保数据一致性")
        self.use_api = True  # 强制保持API模式
    
    def set_api_url(self, url: str):
        """设置API地址"""
        self.api_base_url = url

    def is_api_mode(self) -> bool:
        """是否为API模式"""
        return self.use_api

    def is_local_mode(self) -> bool:
        """是否为本地数据库模式"""
        return not self.use_api

    def get_mode_name(self) -> str:
        """获取当前模式名称"""
        return "服务端API" if self.use_api else "本地数据库"

    def get_endpoint_url(self, endpoint: str, **kwargs) -> str:
        """获取端点的完整URL"""
        url = f"{self.api_base_url}{endpoint}"
        if kwargs:
            url = url.format(**kwargs)
        return url

# API端点常量
class APIEndpoints:
    """API端点常量"""

    # 用户相关
    USERS = "/api/v1/users"
    USER_BY_ID = "/api/v1/users/{user_id}"
    USER_PROFILE = "/api/v1/users/{user_id}/profile"
    USER_PROGRESS = "/api/v1/users/{user_id}/progress"
    USER_PROGRESS_VIDEO = "/api/v1/users/{user_id}/progress/{video_id}"
    USER_CACHE = "/api/v1/users/{user_id}/cache"
    USER_FAVORITES = "/api/v1/users/{user_id}/favorites"

    # 管理端用户 - v1版本化
    ADMIN_USERS = "/api/admin/v1/users"
    ADMIN_USER_BY_ID = "/api/admin/v1/users/{user_id}"
    ADMIN_USER_PURCHASES = "/api/admin/v1/users/{user_id}/purchases"
    ADMIN_USER_PURCHASE_BY_ID = "/api/admin/v1/users/{user_id}/purchases/{purchase_id}"
    ADMIN_USER_PROGRESS = "/api/admin/v1/users/{user_id}/progress"
    ADMIN_USER_PROGRESS_BY_VIDEO = "/api/admin/v1/users/{user_id}/progress/{video_id}"
    ADMIN_USER_FAVORITES = "/api/admin/v1/users/{user_id}/favorites"
    ADMIN_USER_FAVORITE_BY_ID = "/api/admin/v1/users/{user_id}/favorites/{item_id}"
    ADMIN_USER_SETTINGS = "/api/admin/v1/users/{user_id}/settings"
    ADMIN_USER_CACHE = "/api/admin/v1/users/{user_id}/cache"
    ADMIN_USER_CACHE_BY_VIDEO = "/api/admin/v1/users/{user_id}/cache/{video_id}"
    ADMIN_USER_RESET_PASSWORD = "/api/admin/v1/users/{user_id}/reset-password"
    ADMIN_USER_TOGGLE_STATUS = "/api/admin/v1/users/{user_id}/toggle-status"
    ADMIN_USER_TOGGLE_ADMIN = "/api/admin/v1/users/{user_id}/toggle-admin"

    # 系列相关 - v1版本化
    SERIES = "/api/v1/series"
    SERIES_BY_ID = "/api/v1/series/{series_id}"
    SERIES_CATEGORIES = "/api/v1/series/{series_id}/categories"

    # 管理端系列 - v1版本化
    ADMIN_SERIES = "/api/admin/v1/series"
    ADMIN_SERIES_BY_ID = "/api/admin/v1/series/{series_id}"

    # 分类相关 - v1版本化
    CATEGORIES = "/api/v1/categories"
    CATEGORY_BY_ID = "/api/v1/categories/{category_id}"
    CATEGORY_VIDEOS = "/api/v1/categories/{category_id}/videos"

    # 管理端分类 - v1版本化
    ADMIN_CATEGORIES = "/api/admin/v1/categories"
    ADMIN_CATEGORY_BY_ID = "/api/admin/v1/categories/{category_id}"
    ADMIN_CATEGORY_CASCADE_DELETE = "/api/admin/v1/categories/{category_id}/cascade"

    # 管理端系列级联操作 - v1版本化
    ADMIN_SERIES_CASCADE_DELETE = "/api/admin/v1/series/{series_id}/cascade"

    # 批量操作端点 - v1版本化
    ADMIN_SERIES_BATCH_CASCADE = "/api/admin/v1/series/batch-cascade"
    ADMIN_CATEGORIES_BATCH_CASCADE = "/api/admin/v1/categories/batch-cascade"

    # 视频相关 - v1版本化
    VIDEOS = "/api/v1/videos"
    VIDEO_BY_ID = "/api/v1/videos/{video_id}"

    # 管理端视频 - v1版本化
    ADMIN_VIDEOS = "/api/admin/v1/videos"
    ADMIN_VIDEO_BY_ID = "/api/admin/v1/videos/{video_id}"

# 全局配置实例
api_config = APIConfig()
