#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户详情窗口
显示用户的详细信息，包括基本信息、购买记录、学习进度等
"""

import sys
from typing import Dict, Any, List, Optional
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, QLabel, 
    QTableWidget, QTableWidgetItem, QPushButton, QLineEdit,
    QComboBox, QTextEdit, QGroupBox, QFormLayout, QScrollArea,
    QMessageBox, QProgressBar, QFrame, QSplitter, QHeaderView
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QPixmap, QIcon, QColor
from services.user_service import UserService
from ui.thread_safe_ui_helper import get_thread_safe_ui_helper, safe_ui_call
from database.models import DatabaseManager
from utils.table_column_manager import setup_table_with_column_management
import logging

logger = logging.getLogger(__name__)

class UserDetailLoadThread(QThread):
    """用户详情数据加载线程"""
    data_loaded = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, user_service: UserService, user_id: str):
        super().__init__()
        self.user_service = user_service
        self.user_id = user_id
    
    def run(self):
        try:
            # 获取用户基本信息
            user_info = self.user_service.get_user_by_id(self.user_id)

            # 获取用户购买记录
            purchases = self.user_service.get_user_purchases(self.user_id)

            # 获取用户观看进度
            progress = self.user_service.get_user_progress(self.user_id)

            # 获取用户收藏
            favorites = self.user_service.get_user_favorites(self.user_id)

            # 获取用户设置
            settings = self.user_service.get_user_settings(self.user_id)

            # 获取用户缓存
            cache_info = self.user_service.get_user_cache(self.user_id)

            result = {
                'success': True,
                'user_info': user_info,
                'purchases': purchases,
                'progress': progress,
                'favorites': favorites,
                'settings': settings,
                'cache_info': cache_info
            }

            self.data_loaded.emit(result)

        except Exception as e:
            import traceback
            traceback.print_exc()
            self.error_occurred.emit(str(e))

class UserDetailWindow(QWidget):
    """用户详情窗口"""
    
    # 🎯 定义完整的乐观更新信号 - 符合三层同步规则v7.0
    operation_started = pyqtSignal(str, str)  # entity_id, operation_type
    operation_completed = pyqtSignal(str, str, bool)  # entity_id, operation_type, success
    operation_failed = pyqtSignal(str, str, str)  # entity_id, operation_type, error_message
    progress_updated = pyqtSignal(str, int)  # entity_id, progress_percent
    
    def __init__(self, user_id: str, user_service: UserService, parent=None):
        super().__init__(parent)
        self.user_id = user_id
        self.user_service = user_service
        self.load_thread = None

        self.setWindowTitle(f"用户详情 - {user_id}")
        self.setMinimumSize(1000, 700)

        # 设置窗口样式 - 经典管理端风格
        self.setStyleSheet("""
            QWidget {
                background-color: #ffffff;
                color: #333333;
                font-family: "Microsoft YaHei", "SimHei", sans-serif;
                font-size: 12px;
            }

            QTabWidget::pane {
                border: 1px solid #d0d0d0;
                background-color: #ffffff;
                border-radius: 4px;
            }

            QTabWidget::tab-bar {
                alignment: left;
            }

            QTabBar::tab {
                background-color: #f5f5f5;
                border: 1px solid #d0d0d0;
                border-bottom: none;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                min-width: 80px;
            }

            QTabBar::tab:selected {
                background-color: #ffffff;
                border-bottom: 1px solid #ffffff;
                font-weight: bold;
            }

            QTabBar::tab:hover {
                background-color: #e8f4fd;
            }

            QGroupBox {
                font-weight: bold;
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: #fafafa;
            }

            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                background-color: #ffffff;
                border: 1px solid #d0d0d0;
                border-radius: 3px;
            }

            QLabel {
                color: #333333;
                padding: 2px;
            }

            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 3px;
                font-weight: bold;
            }

            QPushButton:hover {
                background-color: #45a049;
            }

            QPushButton:pressed {
                background-color: #3d8b40;
            }

            QProgressBar {
                border: 1px solid #d0d0d0;
                border-radius: 3px;
                text-align: center;
                background-color: #f0f0f0;
            }

            QProgressBar::chunk {
                background-color: #4CAF50;
                border-radius: 2px;
            }

            QTableWidget {
                gridline-color: #e0e0e0;
                background-color: #ffffff;
                alternate-background-color: #f8f9fa;
                selection-background-color: #e3f2fd;
                border: 1px solid #d0d0d0;
                border-radius: 4px;
            }

            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
            }

            QTableWidget::item:selected {
                background-color: #e3f2fd;
                color: #1976d2;
            }

            QTableWidget::item:hover {
                background-color: #f5f5f5;
            }

            QHeaderView::section {
                background-color: #f8f9fa;
                color: #333333;
                padding: 8px;
                border: 1px solid #e0e0e0;
                font-weight: bold;
            }

            QHeaderView::section:hover {
                background-color: #e9ecef;
            }
        """)

        self.init_ui()
        self.load_user_detail()
    
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 标题区域
        title_layout = QHBoxLayout()
        self.title_label = QLabel(f"用户详情")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        self.title_label.setFont(title_font)
        title_layout.addWidget(self.title_label)
        title_layout.addStretch()
        
        # 刷新按钮
        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.clicked.connect(self.load_user_detail)
        title_layout.addWidget(self.refresh_btn)
        
        layout.addLayout(title_layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # 标签页
        self.tab_widget = QTabWidget()
        
        # 基本信息标签页
        self.basic_info_tab = self.create_basic_info_tab()
        self.tab_widget.addTab(self.basic_info_tab, "基本信息")
        
        # 购买记录标签页
        self.purchases_tab = self.create_purchases_tab()
        self.tab_widget.addTab(self.purchases_tab, "购买记录")
        
        # 学习进度标签页
        self.progress_tab = self.create_progress_tab()
        self.tab_widget.addTab(self.progress_tab, "学习进度")
        
        # 收藏管理标签页
        self.favorites_tab = self.create_favorites_tab()
        self.tab_widget.addTab(self.favorites_tab, "收藏管理")
        
        # 用户设置标签页
        self.settings_tab = self.create_settings_tab()
        self.tab_widget.addTab(self.settings_tab, "用户设置")
        
        # 设备缓存标签页
        self.cache_tab = self.create_cache_tab()
        self.tab_widget.addTab(self.cache_tab, "设备缓存")
        
        layout.addWidget(self.tab_widget)
    
    def create_basic_info_tab(self) -> QWidget:
        """创建基本信息标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # 用户头像和基本信息卡片
        info_group = QGroupBox("👤 基本信息")
        info_layout = QFormLayout(info_group)
        info_layout.setSpacing(12)
        info_layout.setContentsMargins(20, 25, 20, 20)

        # 创建标签，设置更好的样式
        self.username_label = QLabel("-")
        self.username_label.setStyleSheet("font-weight: bold; color: #2c3e50;")

        self.email_label = QLabel("-")
        self.email_label.setStyleSheet("color: #34495e;")

        self.display_name_label = QLabel("-")
        self.display_name_label.setStyleSheet("color: #34495e;")

        self.phone_label = QLabel("-")
        self.phone_label.setStyleSheet("color: #34495e;")

        # 🎯 新增：密码字段显示
        self.password_label = QLabel("-")
        self.password_label.setStyleSheet("color: #34495e; font-family: monospace;")

        self.status_label = QLabel("-")
        self.admin_label = QLabel("-")
        self.created_label = QLabel("-")
        self.created_label.setStyleSheet("color: #7f8c8d;")

        self.last_login_label = QLabel("-")
        self.last_login_label.setStyleSheet("color: #7f8c8d;")

        # 🎯 修复：调整字段顺序，密码在用户名下面
        info_layout.addRow("👤 用户名:", self.username_label)
        info_layout.addRow("🔐 密码:", self.password_label)
        info_layout.addRow("📧 邮箱:", self.email_label)
        info_layout.addRow("🏷️ 显示名:", self.display_name_label)
        info_layout.addRow("📱 手机号:", self.phone_label)
        info_layout.addRow("🔄 状态:", self.status_label)
        info_layout.addRow("🔑 权限:", self.admin_label)
        info_layout.addRow("📅 注册时间:", self.created_label)
        info_layout.addRow("🕐 最后登录:", self.last_login_label)

        layout.addWidget(info_group)

        # 操作按钮卡片
        btn_group = QGroupBox("⚙️ 操作")
        btn_group_layout = QVBoxLayout(btn_group)
        btn_group_layout.setContentsMargins(20, 25, 20, 20)

        btn_layout = QHBoxLayout()
        btn_layout.setSpacing(10)

        self.edit_info_btn = QPushButton("✏️ 编辑信息")
        self.edit_info_btn.setStyleSheet("QPushButton { background-color: #3498db; }")
        # 🎯 新增：绑定事件处理函数
        self.edit_info_btn.clicked.connect(self.edit_user_info)

        self.reset_password_btn = QPushButton("🔒 重置密码")
        self.reset_password_btn.setStyleSheet("QPushButton { background-color: #e74c3c; }")
        # 🎯 新增：绑定事件处理函数
        self.reset_password_btn.clicked.connect(self.reset_user_password)

        self.toggle_status_btn = QPushButton("🔄 切换状态")
        self.toggle_status_btn.setStyleSheet("QPushButton { background-color: #f39c12; }")
        # 🎯 新增：绑定事件处理函数
        self.toggle_status_btn.clicked.connect(self.toggle_user_status)

        self.toggle_admin_btn = QPushButton("👑 切换权限")
        self.toggle_admin_btn.setStyleSheet("QPushButton { background-color: #9b59b6; }")
        # 🎯 新增：绑定事件处理函数
        self.toggle_admin_btn.clicked.connect(self.toggle_user_admin)

        btn_layout.addWidget(self.edit_info_btn)
        btn_layout.addWidget(self.reset_password_btn)
        btn_layout.addWidget(self.toggle_status_btn)
        btn_layout.addWidget(self.toggle_admin_btn)
        btn_layout.addStretch()

        btn_group_layout.addLayout(btn_layout)
        layout.addWidget(btn_group)

        layout.addStretch()

        return widget
    
    def create_purchases_tab(self) -> QWidget:
        """创建购买记录标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # 操作按钮组
        btn_group = QGroupBox("💰 购买记录管理")
        btn_group_layout = QVBoxLayout(btn_group)
        btn_group_layout.setContentsMargins(20, 25, 20, 20)

        btn_layout = QHBoxLayout()
        btn_layout.setSpacing(10)

        self.add_purchase_btn = QPushButton("➕ 添加购买记录")
        self.add_purchase_btn.setStyleSheet("QPushButton { background-color: #27ae60; }")

        self.delete_purchase_btn = QPushButton("🗑️ 删除选中记录")
        self.delete_purchase_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: 1px solid #e74c3c;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #c0392b;
                border: 1px solid #c0392b;
            }
        """)

        btn_layout.addWidget(self.add_purchase_btn)
        btn_layout.addWidget(self.delete_purchase_btn)
        btn_layout.addStretch()

        btn_group_layout.addLayout(btn_layout)
        layout.addWidget(btn_group)

        # 购买记录表格
        table_group = QGroupBox("📋 购买记录列表")
        table_layout = QVBoxLayout(table_group)
        table_layout.setContentsMargins(20, 25, 20, 20)

        self.purchases_table = QTableWidget()
        self.purchases_table.setColumnCount(8)
        self.purchases_table.setHorizontalHeaderLabels([
            "购买ID", "分类名称", "所属系列", "包含视频数", "价格", "购买时间", "状态", "有效期"
        ])
        self.purchases_table.horizontalHeader().setStretchLastSection(True)
        self.purchases_table.setAlternatingRowColors(True)
        self.purchases_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        
        # 🔧 添加列宽管理
        purchase_default_widths = [100, 200, 150, 100, 100, 150, 80, 120]  # 购买ID, 分类名称, 所属系列, 包含视频数, 价格, 购买时间, 状态, 有效期
        setup_table_with_column_management(self.purchases_table, 'user_detail_purchases_table', purchase_default_widths)

        table_layout.addWidget(self.purchases_table)
        layout.addWidget(table_group)

        return widget
    
    def create_progress_tab(self) -> QWidget:
        """创建学习进度标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # 操作按钮组
        btn_group = QGroupBox("📊 学习进度管理")
        btn_group_layout = QVBoxLayout(btn_group)
        btn_group_layout.setContentsMargins(20, 25, 20, 20)

        btn_layout = QHBoxLayout()
        btn_layout.setSpacing(10)

        self.reset_progress_btn = QPushButton("🔄 重置选中进度")
        self.reset_progress_btn.setStyleSheet("QPushButton { background-color: #f39c12; }")

        self.mark_completed_btn = QPushButton("✅ 标记为完成")
        self.mark_completed_btn.setStyleSheet("QPushButton { background-color: #27ae60; }")

        btn_layout.addWidget(self.reset_progress_btn)
        btn_layout.addWidget(self.mark_completed_btn)
        btn_layout.addStretch()

        btn_group_layout.addLayout(btn_layout)
        layout.addWidget(btn_group)

        # 学习进度表格
        table_group = QGroupBox("📈 学习进度详情")
        table_layout = QVBoxLayout(table_group)
        table_layout.setContentsMargins(20, 25, 20, 20)

        self.progress_table = QTableWidget()
        self.progress_table.setColumnCount(7)
        self.progress_table.setHorizontalHeaderLabels([
            "视频ID", "视频标题", "所属分类", "所属系列", "观看进度", "观看次数", "最后观看"
        ])
        self.progress_table.horizontalHeader().setStretchLastSection(True)
        self.progress_table.setAlternatingRowColors(True)
        self.progress_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        
        # 🔧 添加列宽管理
        progress_default_widths = [80, 180, 120, 120, 150, 80, 150]  # 视频ID, 视频标题, 所属分类, 所属系列, 观看进度, 观看次数, 最后观看
        setup_table_with_column_management(self.progress_table, 'user_detail_progress_table', progress_default_widths)

        table_layout.addWidget(self.progress_table)
        layout.addWidget(table_group)

        return widget
    
    def create_favorites_tab(self) -> QWidget:
        """创建收藏管理标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # 操作按钮组
        btn_group = QGroupBox("❤️ 收藏管理")
        btn_group_layout = QVBoxLayout(btn_group)
        btn_group_layout.setContentsMargins(20, 25, 20, 20)

        btn_layout = QHBoxLayout()
        self.delete_favorite_btn = QPushButton("🗑️ 删除选中收藏")
        self.delete_favorite_btn.setStyleSheet("QPushButton { background-color: #e74c3c; }")
        btn_layout.addWidget(self.delete_favorite_btn)
        btn_layout.addStretch()

        btn_group_layout.addLayout(btn_layout)
        layout.addWidget(btn_group)

        # 收藏表格
        table_group = QGroupBox("📋 收藏列表")
        table_layout = QVBoxLayout(table_group)
        table_layout.setContentsMargins(20, 25, 20, 20)

        self.favorites_table = QTableWidget()
        self.favorites_table.setColumnCount(6)
        self.favorites_table.setHorizontalHeaderLabels([
            "视频ID", "视频标题", "所属分类", "所属系列", "收藏时间", "操作"
        ])
        self.favorites_table.horizontalHeader().setStretchLastSection(True)
        self.favorites_table.setAlternatingRowColors(True)
        self.favorites_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        
        # 🔧 添加列宽管理
        favorites_default_widths = [80, 180, 120, 120, 150, 80]  # 视频ID, 视频标题, 所属分类, 所属系列, 收藏时间, 操作
        setup_table_with_column_management(self.favorites_table, 'user_detail_favorites_table', favorites_default_widths)

        table_layout.addWidget(self.favorites_table)
        layout.addWidget(table_group)

        return widget

    def create_settings_tab(self) -> QWidget:
        """创建用户设置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # 设置表格
        table_group = QGroupBox("⚙️ 用户设置")
        table_layout = QVBoxLayout(table_group)
        table_layout.setContentsMargins(20, 25, 20, 20)

        self.settings_table = QTableWidget()
        self.settings_table.setColumnCount(3)
        self.settings_table.setHorizontalHeaderLabels([
            "设置键", "设置值", "操作"
        ])
        self.settings_table.horizontalHeader().setStretchLastSection(True)
        self.settings_table.setAlternatingRowColors(True)
        self.settings_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        
        # 🔧 添加列宽管理
        settings_default_widths = [200, 300, 100]
        setup_table_with_column_management(self.settings_table, 'user_detail_settings_table', settings_default_widths)

        table_layout.addWidget(self.settings_table)
        layout.addWidget(table_group)

        return widget

    def create_cache_tab(self) -> QWidget:
        """创建设备缓存标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # 操作按钮组
        btn_group = QGroupBox("💾 缓存管理")
        btn_group_layout = QVBoxLayout(btn_group)
        btn_group_layout.setContentsMargins(20, 25, 20, 20)

        btn_layout = QHBoxLayout()
        btn_layout.setSpacing(10)

        self.clear_cache_btn = QPushButton("🗑️ 清除选中缓存")
        self.clear_cache_btn.setStyleSheet("QPushButton { background-color: #e74c3c; }")

        self.clear_all_cache_btn = QPushButton("🧹 清除所有缓存")
        self.clear_all_cache_btn.setStyleSheet("QPushButton { background-color: #c0392b; }")

        btn_layout.addWidget(self.clear_cache_btn)
        btn_layout.addWidget(self.clear_all_cache_btn)
        btn_layout.addStretch()

        btn_group_layout.addLayout(btn_layout)
        layout.addWidget(btn_group)

        # 缓存表格
        table_group = QGroupBox("📋 缓存列表")
        table_layout = QVBoxLayout(table_group)
        table_layout.setContentsMargins(20, 25, 20, 20)

        self.cache_table = QTableWidget()
        self.cache_table.setColumnCount(7)
        self.cache_table.setHorizontalHeaderLabels([
            "视频ID", "视频标题", "所属分类", "所属系列", "缓存状态", "文件大小", "缓存时间"
        ])
        self.cache_table.horizontalHeader().setStretchLastSection(True)
        self.cache_table.setAlternatingRowColors(True)
        self.cache_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        
        # 🔧 添加列宽管理
        cache_default_widths = [80, 160, 120, 120, 100, 100, 150]  # 视频ID, 视频标题, 所属分类, 所属系列, 缓存状态, 文件大小, 缓存时间
        setup_table_with_column_management(self.cache_table, 'user_detail_cache_table', cache_default_widths)

        table_layout.addWidget(self.cache_table)
        layout.addWidget(table_group)

        return widget
    
    def load_user_detail(self):
        """加载用户详情数据"""
        if self.load_thread and self.load_thread.isRunning():
            return
        
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 无限进度条
        
        self.load_thread = UserDetailLoadThread(self.user_service, self.user_id)
        self.load_thread.data_loaded.connect(self.on_data_loaded)
        self.load_thread.error_occurred.connect(self.on_load_error)
        self.load_thread.start()
    
    def on_data_loaded(self, result):
        """数据加载完成"""
        self.progress_bar.setVisible(False)
        
        if result['success']:
            self.populate_user_info(result['user_info'])
            self.populate_purchases(result['purchases'])
            self.populate_progress(result['progress'])
            self.populate_favorites(result['favorites'])
            self.populate_settings(result['settings'])
            self.populate_cache(result['cache_info'])
        else:
            QMessageBox.warning(self, '错误', result.get('message', '加载用户详情失败'))
    
    def on_load_error(self, error_msg):
        """数据加载错误"""
        self.progress_bar.setVisible(False)
        QMessageBox.critical(self, '错误', f'加载用户详情失败: {error_msg}')
    
    def populate_user_info(self, user_info):
        """填充用户基本信息"""
        if not user_info:
            return

        # 处理嵌套的数据结构
        if user_info.get('success') and user_info.get('data'):
            user_data = user_info['data']
        else:
            user_data = user_info

        self.username_label.setText(user_data.get('username', '-'))
        self.email_label.setText(user_data.get('email', '-'))
        self.display_name_label.setText(user_data.get('display_name', '-'))
        self.phone_label.setText(user_data.get('phone', '-'))

        # 🎯 新增：密码显示为明文（管理端专用）
        password_hash = user_data.get('password_hash', '')
        print(f"🔍 调试：用户数据中的密码字段 - password_hash: '{password_hash}'")
        print(f"🔍 调试：用户数据的所有字段: {list(user_data.keys())}")

        if password_hash:
            self.password_label.setText(password_hash)
            print(f"✅ 密码显示设置为: '{password_hash}'")
        else:
            self.password_label.setText('未设置')
            print("⚠️ 密码字段为空，显示'未设置'")

        # 状态显示 - 使用图标和颜色
        is_active = user_data.get('is_active', False)
        if is_active:
            status_html = '<span style="color: #27ae60; font-weight: bold;">🟢 激活</span>'
        else:
            status_html = '<span style="color: #e74c3c; font-weight: bold;">🔴 禁用</span>'
        self.status_label.setText(status_html)

        # 权限显示 - 使用图标和颜色
        is_admin = user_data.get('is_admin', False)
        if is_admin:
            admin_html = '<span style="color: #8e44ad; font-weight: bold;">👑 管理员</span>'
        else:
            admin_html = '<span style="color: #34495e; font-weight: bold;">👤 普通用户</span>'
        self.admin_label.setText(admin_html)

        self.created_label.setText(user_data.get('created_at', '-'))
        self.last_login_label.setText(user_data.get('last_login_at', '-'))

        # 更新窗口标题
        username = user_data.get('username', self.user_id)
        display_name = user_data.get('display_name', '')
        title = f"用户详情 - {username}"
        if display_name and display_name != username:
            title += f" ({display_name})"
        self.setWindowTitle(title)
        self.title_label.setText(title)
    
    def populate_purchases(self, purchases):
        """填充购买记录（以分类为单位显示）"""
        if not purchases or not purchases.get('success'):
            self.purchases_table.setRowCount(0)
            return

        purchase_data = purchases.get('data', [])
        self.purchases_table.setRowCount(len(purchase_data))

        for row, purchase in enumerate(purchase_data):
            # 购买ID
            self.purchases_table.setItem(row, 0, QTableWidgetItem(str(purchase.get('id', ''))))

            # 分类名称（优先显示，如果是系列级购买则显示系列名）
            item_type = purchase.get('type', '')
            if item_type == 'category':
                category_name = purchase.get('item_name', '')
                self.purchases_table.setItem(row, 1, QTableWidgetItem(category_name))
            elif item_type == 'series':
                series_name = purchase.get('item_name', '')
                self.purchases_table.setItem(row, 1, QTableWidgetItem(f"[系列] {series_name}"))
            else:
                self.purchases_table.setItem(row, 1, QTableWidgetItem(purchase.get('item_name', '')))

            # 所属系列（如果是分类购买，显示其所属系列；如果是系列购买，显示系列名）
            series_title = purchase.get('series_title', '') or purchase.get('parent_series', '')
            self.purchases_table.setItem(row, 2, QTableWidgetItem(series_title))

            # 包含视频数（需要从API获取或估算）
            video_count = purchase.get('video_count', 0)
            if video_count > 0:
                count_text = f"{video_count} 个视频"
            else:
                count_text = "待统计"
            self.purchases_table.setItem(row, 3, QTableWidgetItem(count_text))

            # 价格
            amount = purchase.get('amount', 0)
            price_text = f"¥{amount:.2f}" if amount > 0 else "免费"
            self.purchases_table.setItem(row, 4, QTableWidgetItem(price_text))

            # 购买时间
            purchase_date = purchase.get('purchase_date', '')
            if purchase_date:
                try:
                    from datetime import datetime
                    dt = datetime.fromisoformat(purchase_date.replace('Z', '+00:00'))
                    formatted_date = dt.strftime('%Y-%m-%d %H:%M')
                except:
                    formatted_date = purchase_date
            else:
                formatted_date = '-'
            self.purchases_table.setItem(row, 5, QTableWidgetItem(formatted_date))

            # 状态
            status = purchase.get('status', 'unknown')
            status_text = "已完成" if status == "completed" else "处理中" if status == "pending" else "未知"
            status_item = QTableWidgetItem(status_text)
            if status == "completed":
                status_item.setBackground(QColor(200, 255, 200))  # 绿色背景
            elif status == "pending":
                status_item.setBackground(QColor(255, 255, 200))  # 黄色背景
            self.purchases_table.setItem(row, 6, status_item)

            # 有效期
            expiry_date = purchase.get('expiry_date', '')
            if expiry_date:
                try:
                    from datetime import datetime
                    dt = datetime.fromisoformat(expiry_date.replace('Z', '+00:00'))
                    formatted_expiry = dt.strftime('%Y-%m-%d')
                except:
                    formatted_expiry = expiry_date
            else:
                formatted_expiry = '永久'
            self.purchases_table.setItem(row, 7, QTableWidgetItem(formatted_expiry))
    
    def populate_progress(self, progress):
        """填充学习进度（以视频为单位，显示分类、系列信息）"""
        if not progress or not progress.get('success'):
            self.progress_table.setRowCount(0)
            return

        progress_data = progress.get('data', [])
        self.progress_table.setRowCount(len(progress_data))

        for row, prog in enumerate(progress_data):
            # 视频ID
            self.progress_table.setItem(row, 0, QTableWidgetItem(str(prog.get('video_id', ''))))

            # 视频标题
            video_title = prog.get('video_title', '') or prog.get('title', '')
            self.progress_table.setItem(row, 1, QTableWidgetItem(video_title))

            # 所属分类
            category_title = prog.get('category_title', '') or prog.get('category_name', '')
            self.progress_table.setItem(row, 2, QTableWidgetItem(category_title))

            # 所属系列  
            series_title = prog.get('series_title', '') or prog.get('series_name', '')
            self.progress_table.setItem(row, 3, QTableWidgetItem(series_title))

            # 观看进度
            progress_seconds = prog.get('progress_seconds', 0)
            total_duration = prog.get('total_duration', 0)
            progress_percentage = prog.get('progress_percentage', 0)

            if total_duration > 0:
                # 转换为分钟:秒格式
                progress_min = progress_seconds // 60
                progress_sec = progress_seconds % 60
                total_min = total_duration // 60
                total_sec = total_duration % 60
                progress_text = f"{progress_min:02d}:{progress_sec:02d} / {total_min:02d}:{total_sec:02d} ({progress_percentage:.1f}%)"
            else:
                progress_text = f"{progress_seconds}s"

            progress_item = QTableWidgetItem(progress_text)
            # 根据进度设置背景色
            if progress_percentage >= 100:
                progress_item.setBackground(QColor(200, 255, 200))  # 绿色 - 已完成
            elif progress_percentage >= 50:
                progress_item.setBackground(QColor(255, 255, 200))  # 黄色 - 进行中
            elif progress_percentage > 0:
                progress_item.setBackground(QColor(255, 230, 230))  # 浅红色 - 刚开始

            self.progress_table.setItem(row, 4, progress_item)

            # 观看次数（显示完成次数）
            watch_count = prog.get('watch_count', 0)
            completed_count = prog.get('completed_count', 0)
            if completed_count > 0:
                count_text = f"{watch_count} 次 (完成 {completed_count} 次)"
            else:
                count_text = f"{watch_count} 次"
            self.progress_table.setItem(row, 5, QTableWidgetItem(count_text))

            # 最后观看时间
            last_watched = prog.get('last_watched', '')
            if last_watched:
                try:
                    from datetime import datetime
                    dt = datetime.fromisoformat(last_watched.replace('Z', '+00:00'))
                    formatted_date = dt.strftime('%Y-%m-%d %H:%M')
                except:
                    formatted_date = last_watched
            else:
                formatted_date = '-'
            self.progress_table.setItem(row, 6, QTableWidgetItem(formatted_date))
    
    def populate_favorites(self, favorites):
        """填充收藏记录（以视频为单位显示）"""
        if not favorites or not favorites.get('success'):
            self.favorites_table.setRowCount(0)
            return

        favorites_data = favorites.get('data', [])
        self.favorites_table.setRowCount(len(favorites_data))

        for row, favorite in enumerate(favorites_data):
            # 视频ID
            video_id = favorite.get('item_id', '') if favorite.get('type') == 'video' else favorite.get('video_id', '')
            self.favorites_table.setItem(row, 0, QTableWidgetItem(str(video_id)))

            # 视频标题（如果是视频收藏，显示视频标题；否则显示相应的名称）
            item_type = favorite.get('type', '')
            if item_type == 'video':
                video_title = favorite.get('item_name', '') or favorite.get('video_title', '')
            elif item_type == 'category':
                video_title = f"[分类] {favorite.get('item_name', '')}"
            elif item_type == 'series':
                video_title = f"[系列] {favorite.get('item_name', '')}"
            else:
                video_title = favorite.get('item_name', '')
            
            self.favorites_table.setItem(row, 1, QTableWidgetItem(video_title))

            # 所属分类
            category_title = favorite.get('category_title', '') or favorite.get('category_name', '')
            if item_type == 'category':
                category_title = favorite.get('item_name', '')  # 如果收藏的是分类，显示分类名
            self.favorites_table.setItem(row, 2, QTableWidgetItem(category_title))

            # 所属系列
            series_title = favorite.get('series_title', '') or favorite.get('series_name', '')
            if item_type == 'series':
                series_title = favorite.get('item_name', '')  # 如果收藏的是系列，显示系列名
            self.favorites_table.setItem(row, 3, QTableWidgetItem(series_title))

            # 收藏时间
            created_at = favorite.get('created_at', '')
            if created_at:
                try:
                    from datetime import datetime
                    dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                    formatted_date = dt.strftime('%Y-%m-%d %H:%M')
                except:
                    formatted_date = created_at
            else:
                formatted_date = '-'
            self.favorites_table.setItem(row, 4, QTableWidgetItem(formatted_date))

            # 操作按钮（暂时显示文本）
            self.favorites_table.setItem(row, 5, QTableWidgetItem("删除"))
    
    def populate_settings(self, settings):
        """填充用户设置"""
        if not settings or not settings.get('success'):
            self.settings_table.setRowCount(0)
            return

        settings_data = settings.get('data', [])
        self.settings_table.setRowCount(len(settings_data))

        for row, setting in enumerate(settings_data):
            # 设置键
            self.settings_table.setItem(row, 0, QTableWidgetItem(setting.get('key', '')))

            # 设置值
            value = setting.get('value', '')
            # 限制显示长度
            if len(str(value)) > 50:
                display_value = str(value)[:47] + "..."
            else:
                display_value = str(value)
            self.settings_table.setItem(row, 1, QTableWidgetItem(display_value))

            # 操作按钮（暂时显示文本）
            self.settings_table.setItem(row, 2, QTableWidgetItem("编辑"))

    def populate_cache(self, cache_info):
        """填充缓存信息（以视频为单位显示）"""
        if not cache_info or not cache_info.get('success'):
            self.cache_table.setRowCount(0)
            return

        cache_data = cache_info.get('data', [])
        self.cache_table.setRowCount(len(cache_data))

        for row, cache in enumerate(cache_data):
            # 视频ID
            self.cache_table.setItem(row, 0, QTableWidgetItem(str(cache.get('video_id', ''))))

            # 视频标题
            video_title = cache.get('video_title', '') or cache.get('title', '')
            self.cache_table.setItem(row, 1, QTableWidgetItem(video_title))

            # 所属分类
            category_title = cache.get('category_title', '') or cache.get('category_name', '')
            self.cache_table.setItem(row, 2, QTableWidgetItem(category_title))

            # 所属系列
            series_title = cache.get('series_title', '') or cache.get('series_name', '')
            self.cache_table.setItem(row, 3, QTableWidgetItem(series_title))

            # 缓存状态
            cache_status = cache.get('cache_status', False) or cache.get('is_cached', False)
            status_text = "已缓存" if cache_status else "未缓存"
            status_item = QTableWidgetItem(status_text)
            if cache_status:
                status_item.setBackground(QColor(200, 255, 200))  # 绿色背景
            else:
                status_item.setBackground(QColor(255, 230, 230))  # 浅红色背景
            self.cache_table.setItem(row, 4, status_item)

            # 文件大小
            file_size = cache.get('file_size', 0) or cache.get('size_bytes', 0)
            if file_size > 0:
                # 转换为可读的文件大小
                if file_size >= 1024 * 1024 * 1024:  # GB
                    size_text = f"{file_size / (1024 * 1024 * 1024):.2f} GB"
                elif file_size >= 1024 * 1024:  # MB
                    size_text = f"{file_size / (1024 * 1024):.2f} MB"
                elif file_size >= 1024:  # KB
                    size_text = f"{file_size / 1024:.2f} KB"
                else:
                    size_text = f"{file_size} B"
            else:
                size_text = "-"
            self.cache_table.setItem(row, 5, QTableWidgetItem(size_text))

            # 缓存时间
            cached_at = cache.get('cached_at', '') or cache.get('created_at', '')
            if cached_at:
                try:
                    from datetime import datetime
                    dt = datetime.fromisoformat(cached_at.replace('Z', '+00:00'))
                    formatted_date = dt.strftime('%Y-%m-%d %H:%M')
                except:
                    formatted_date = cached_at
            else:
                formatted_date = '-'
            self.cache_table.setItem(row, 6, QTableWidgetItem(formatted_date))

    # 🎯 新增：操作按钮事件处理方法
    def edit_user_info(self):
        """编辑用户信息"""
        try:
            # 导入用户编辑对话框
            from ui.user_window import UserEditDialog

            # 获取当前用户数据
            is_active = '激活' in self.status_label.text()
            is_admin = '管理员' in self.admin_label.text()

            current_user_data = {
                'username': self.username_label.text(),
                'email': self.email_label.text(),
                'display_name': self.display_name_label.text(),
                'phone': self.phone_label.text(),
                'is_active': is_active,
                'is_admin': is_admin
            }

            # 🔍 调试：打印激活状态信息
            print(f"🔍 编辑对话框调试：")
            print(f"  状态标签文本: '{self.status_label.text()}'")
            print(f"  解析的激活状态: {is_active}")
            print(f"  权限标签文本: '{self.admin_label.text()}'")
            print(f"  解析的管理员状态: {is_admin}")

            # 🎯 修复：正确的参数顺序 - parent在前，user_data在后
            dialog = UserEditDialog(parent=self, user_data=current_user_data)
            if dialog.exec() == dialog.DialogCode.Accepted:
                # 获取修改后的数据
                new_data = dialog.get_form_data()

                # 调用API更新用户信息
                try:
                    result = self.user_service.update_user_info(self.user_id, new_data)
                    if result and result.get('success'):
                        QMessageBox.information(self, '成功', '用户信息更新成功！')
                        # 重新加载用户详情
                        self.load_user_detail()
                    else:
                        error_msg = result.get('message', '更新失败') if result else '更新失败'
                        QMessageBox.warning(self, '失败', f'用户信息更新失败：{error_msg}')
                except Exception as api_error:
                    QMessageBox.critical(self, '错误', f'更新用户信息时发生错误：{str(api_error)}')

        except Exception as e:
            QMessageBox.critical(self, '错误', f'打开编辑对话框失败：{str(e)}')

    def reset_user_password(self):
        """重置用户密码 - 🎯 修复：服务端自动生成密码"""
        try:
            # 确认重置对话框
            reply = QMessageBox.question(
                self,
                '确认重置密码',
                f'确定要重置用户 {self.username_label.text()} 的密码吗？\n\n系统将自动生成新密码。',
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                # 🎯 修复：只传递user_id参数，服务端自动生成密码
                try:
                    result = self.user_service.reset_user_password(self.user_id)
                    if result and result.get('success'):
                        new_password = result.get('new_password', '未知')
                        QMessageBox.information(
                            self,
                            '密码重置成功',
                            f'用户 {self.username_label.text()} 的密码已重置！\n\n新密码：{new_password}\n\n请妥善保管新密码。'
                        )
                        # 重新加载用户详情以显示新密码
                        self.load_user_detail()
                    else:
                        error_msg = result.get('message', '重置失败') if result else '重置失败'
                        QMessageBox.warning(self, '失败', f'密码重置失败：{error_msg}')
                except Exception as api_error:
                    QMessageBox.critical(self, '错误', f'重置密码时发生错误：{str(api_error)}')

        except Exception as e:
            QMessageBox.critical(self, '错误', f'重置密码失败：{str(e)}')

    def toggle_user_status(self):
        """切换用户状态（激活/禁用）- 🎯 修复：真正的乐观更新"""
        try:
            current_status = '激活' in self.status_label.text()
            new_status = not current_status
            status_text = '激活' if new_status else '禁用'

            # 🎯 乐观更新：立即更新UI
            self.status_label.setText(f"🔄 {status_text}")
            print(f"🚀 乐观更新：立即将用户状态显示为 {status_text}")

            # 🎯 后台API调用
            try:
                result = self.user_service.toggle_user_status(self.user_id)
                if result and result.get('success'):
                    # ✅ 成功：保持UI更新，不弹框
                    print(f"✅ 状态切换成功：{status_text}")
                else:
                    # ❌ 失败：回滚UI并弹框提示
                    original_status_text = '禁用' if new_status else '激活'
                    self.status_label.setText(f"🔄 {original_status_text}")
                    error_msg = result.get('message', '切换失败') if result else '切换失败'
                    QMessageBox.warning(self, '操作失败', f'状态切换失败：{error_msg}\n\n已回滚到原状态。')
                    print(f"❌ 状态切换失败，已回滚：{error_msg}")
            except Exception as api_error:
                # ❌ 异常：回滚UI并弹框提示
                original_status_text = '禁用' if new_status else '激活'
                self.status_label.setText(f"🔄 {original_status_text}")
                QMessageBox.critical(self, '操作异常', f'切换状态时发生错误：{str(api_error)}\n\n已回滚到原状态。')
                print(f"❌ 状态切换异常，已回滚：{str(api_error)}")

        except Exception as e:
            QMessageBox.critical(self, '错误', f'切换用户状态失败：{str(e)}')

    def toggle_user_admin(self):
        """切换用户权限（普通用户/管理员）- 🎯 修复：真正的乐观更新"""
        try:
            current_admin = '管理员' in self.admin_label.text()
            new_admin = not current_admin
            role_text = '管理员' if new_admin else '普通用户'

            # 🎯 乐观更新：立即更新UI
            self.admin_label.setText(f"🔑 {role_text}")
            print(f"🚀 乐观更新：立即将用户权限显示为 {role_text}")

            # 🎯 后台API调用
            try:
                result = self.user_service.toggle_user_admin(self.user_id)
                if result and result.get('success'):
                    # ✅ 成功：保持UI更新，不弹框
                    print(f"✅ 权限切换成功：{role_text}")
                else:
                    # ❌ 失败：回滚UI并弹框提示
                    original_role_text = '普通用户' if new_admin else '管理员'
                    self.admin_label.setText(f"🔑 {original_role_text}")
                    error_msg = result.get('message', '切换失败') if result else '切换失败'
                    QMessageBox.warning(self, '操作失败', f'权限切换失败：{error_msg}\n\n已回滚到原权限。')
                    print(f"❌ 权限切换失败，已回滚：{error_msg}")
            except Exception as api_error:
                # ❌ 异常：回滚UI并弹框提示
                original_role_text = '普通用户' if new_admin else '管理员'
                self.admin_label.setText(f"🔑 {original_role_text}")
                QMessageBox.critical(self, '操作异常', f'切换权限时发生错误：{str(api_error)}\n\n已回滚到原权限。')
                print(f"❌ 权限切换异常，已回滚：{str(api_error)}")

        except Exception as e:
            QMessageBox.critical(self, '错误', f'切换用户权限失败：{str(e)}')
