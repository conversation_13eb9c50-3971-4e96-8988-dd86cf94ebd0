#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全局数据管理器
实现一次性加载所有数据到内存，后续操作完全使用内存数据
"""

import logging
from typing import Dict, List, Any, Optional
from api.client import APIClient, SeriesAPIClient, CategoryAPIClient, VideoAPIClient

logger = logging.getLogger(__name__)

class GlobalDataManager:
    """全局数据管理器 - 一次性加载，内存操作"""
    
    def __init__(self, api_base_url: str = None):
        # 如果没有提供URL，从配置文件读取
        if api_base_url is None:
            try:
                from ..utils.config import Config
                config = Config()
                api_config = config.get_api_config()
                api_base_url = api_config['base_url']
            except:
                api_base_url = "http://localhost:8000"  # 最后的备选方案
        self.api_client = APIClient(api_base_url)
        self.series_client = SeriesAPIClient(self.api_client)
        self.category_client = CategoryAPIClient(self.api_client)
        self.video_client = VideoAPIClient(self.api_client)
        
        # 内存数据存储
        self._series_data: List[Dict[str, Any]] = []
        self._category_data: List[Dict[str, Any]] = []
        self._video_data: List[Dict[str, Any]] = []
        self._user_data: List[Dict[str, Any]] = []  # 添加用户数据

        # 统计数据缓存
        self._statistics: Dict[str, int] = {}

        # 加载状态
        self._data_loaded = False
        
    def load_all_data_once(self) -> bool:
        """一次性加载所有数据到内存 - 🎯 强制使用服务端API"""
        if self._data_loaded:
            print("📦 数据已在内存中，跳过重复加载")
            return True

        try:
            print("🚀 开始一次性加载所有数据...")
            print("🌐 管理端强制使用服务端API加载数据...")

            # 🎯 强制使用API模式，确保数据来源一致性
            return self._load_from_api()

        except Exception as e:
            print(f"❌ 服务端数据加载异常: {e}")
            print("🔄 尝试重新连接服务端...")
            # 重试机制而不是降级到本地
            try:
                import time
                time.sleep(2)  # 等待2秒后重试
                return self._load_from_api()
            except Exception as retry_e:
                print(f"❌ 服务端重试失败: {retry_e}")
                return False

    def _load_from_api(self) -> bool:
        """从API加载数据"""
        try:
            # 1. 加载系列数据
            print("📚 加载系列数据...")
            series_result = self.series_client.get_series(page=1, page_size=100)
            if series_result.get('success'):
                self._series_data = series_result.get('data', [])
                print(f"✅ 系列数据加载完成: {len(self._series_data)} 个")
            else:
                print(f"❌ 系列数据加载失败: {series_result.get('message')}")
                # 🎯 关键修复：API失败时不立即降级，继续尝试其他数据
                print("⚠️ 系列数据API失败，但继续尝试其他数据...")
                self._series_data = []

            # 2. 加载分类数据（无分页，获取所有数据）
            print("📂 加载分类数据...")
            category_result = self.category_client.get_all_categories()
            if category_result.get('success'):
                self._category_data = category_result.get('data', [])
                print(f"✅ 分类数据加载完成: {len(self._category_data)} 个")
            else:
                print(f"❌ 分类数据加载失败: {category_result.get('message')}")
                print("⚠️ 分类数据API失败，但继续尝试其他数据...")
                self._category_data = []

            # 3. 加载视频数据（无分页，获取所有数据）
            print("📹 加载视频数据...")
            video_result = self.video_client.get_all_videos()
            if video_result.get('success'):
                self._video_data = video_result.get('data', [])
                print(f"✅ 视频数据加载完成: {len(self._video_data)} 个")
            else:
                print(f"❌ 视频数据加载失败: {video_result.get('message')}")
                print("⚠️ 视频数据API失败，但继续尝试其他数据...")
                self._video_data = []

            # 4. 加载用户数据（分页获取所有用户）
            print("👥 加载用户数据...")
            try:
                self._user_data = []
                page = 1
                page_size = 100  # API限制最大100

                while True:
                    user_response = self.api_client.get('/api/admin/v1/users', {
                        'page': page,
                        'page_size': page_size
                    })

                    if user_response and user_response.get('success'):
                        page_data = user_response.get('data', [])
                        self._user_data.extend(page_data)

                        # 检查是否还有更多页
                        pagination = user_response.get('pagination', {})
                        if page >= pagination.get('pages', 1):
                            break
                        page += 1
                    else:
                        print(f"❌ 用户数据第{page}页加载失败: {user_response.get('message') if user_response else '无响应'}")
                        if page == 1:  # 第一页就失败，尝试本地数据库降级
                            print("🔄 用户数据API完全失败，尝试从本地数据库加载...")
                            self._load_users_from_database()
                        break

                print(f"✅ 用户数据加载完成: {len(self._user_data)} 个")

            except Exception as e:
                print(f"❌ 用户数据API加载异常: {e}")
                print("🔄 用户数据API失败，尝试从本地数据库加载...")
                # 尝试从本地数据库加载用户数据
                self._load_users_from_database()

            # 预加载用户详情数据
            print("🔍 开始预加载用户详情数据...")
            self._user_details = {}
            self._user_purchases = {}
            self._user_progress = {}
            self._user_favorites = {}
            self._user_settings = {}
            self._user_cache = {}

            # 启用用户详情预加载，确保重启后能获取购买记录和编辑的用户信息
            print("🔍 开始预加载用户详情数据...")
            self.preload_user_details()
            print(f"✅ 用户详情数据预加载完成: {len(self._user_details)} 个用户")

            self._data_loaded = True
            print("🎉 所有数据一次性加载完成！")
            print(f"📊 数据统计: 系列={len(self._series_data)}, 分类={len(self._category_data)}, 视频={len(self._video_data)}, 用户={len(self._user_data)}")

            # 计算并缓存统计数据
            self._statistics = self.calculate_statistics()
            print(f"📊 统计数据已缓存: {self._statistics}")

            return True

        except Exception as e:
            print(f"❌ API数据加载异常: {e}")
            return False

    def preload_user_details(self):
        """预加载所有用户的详情数据"""
        if not self._user_data:
            return

        total_users = len(self._user_data)
        for i, user in enumerate(self._user_data):
            user_id = user.get('user_id', user.get('id'))
            if not user_id:
                continue

            print(f"🔍 预加载用户详情 ({i+1}/{total_users}): {user.get('username', user_id)}")

            try:
                # 预加载用户基本信息（已有）
                self._user_details[user_id] = user

                # 预加载用户购买记录
                try:
                    purchases_response = self.api_client.get(f'/api/admin/v1/users/{user_id}/purchases')
                    self._user_purchases[user_id] = purchases_response
                except Exception as e:
                    print(f"   ⚠️ 购买记录加载失败: {e}")
                    self._user_purchases[user_id] = {'success': False, 'data': []}

                # 预加载用户观看进度
                try:
                    progress_response = self.api_client.get(f'/api/admin/v1/users/{user_id}/progress')
                    self._user_progress[user_id] = progress_response
                except Exception as e:
                    print(f"   ⚠️ 观看进度加载失败: {e}")
                    self._user_progress[user_id] = {'success': False, 'data': []}

                # 预加载用户收藏
                try:
                    favorites_response = self.api_client.get(f'/api/admin/v1/users/{user_id}/favorites')
                    self._user_favorites[user_id] = favorites_response
                except Exception as e:
                    print(f"   ⚠️ 收藏数据加载失败: {e}")
                    self._user_favorites[user_id] = {'success': False, 'data': []}

                # 预加载用户设置
                try:
                    settings_response = self.api_client.get(f'/api/admin/v1/users/{user_id}/settings')
                    self._user_settings[user_id] = settings_response
                except Exception as e:
                    print(f"   ⚠️ 用户设置加载失败: {e}")
                    self._user_settings[user_id] = {'success': False, 'data': []}

                # 预加载用户缓存
                try:
                    cache_response = self.api_client.get(f'/api/admin/v1/users/{user_id}/cache')
                    self._user_cache[user_id] = cache_response
                except Exception as e:
                    print(f"   ⚠️ 缓存数据加载失败: {e}")
                    self._user_cache[user_id] = {'success': False, 'data': []}

            except Exception as e:
                print(f"   ❌ 用户 {user_id} 详情预加载失败: {e}")

    def get_user_detail(self, user_id: str):
        """获取预加载的用户详情"""
        return self._user_details.get(user_id)

    def get_user_purchases(self, user_id: str):
        """获取预加载的用户购买记录"""
        return self._user_purchases.get(user_id, {'success': False, 'data': []})

    def get_user_progress(self, user_id: str):
        """获取预加载的用户观看进度"""
        return self._user_progress.get(user_id, {'success': False, 'data': []})

    def get_user_favorites(self, user_id: str):
        """获取预加载的用户收藏"""
        return self._user_favorites.get(user_id, {'success': False, 'data': []})

    def get_user_settings(self, user_id: str):
        """获取预加载的用户设置"""
        return self._user_settings.get(user_id, {'success': False, 'data': []})

    def get_user_cache(self, user_id: str):
        """获取预加载的用户缓存"""
        return self._user_cache.get(user_id, {'success': False, 'data': []})

    def _load_from_database(self) -> bool:
        """从本地数据库加载数据"""
        try:
            from database.models import DatabaseManager, Series, Category, Video
            from utils.config import Config

            # 创建数据库连接
            config = Config()
            db_config = config.get_database_config()
            connection_string = f"mysql+pymysql://{db_config['user']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['database']}?charset={db_config['charset']}"
            db_manager = DatabaseManager(connection_string)
            session = db_manager.get_session()

            # 1. 加载系列数据
            print("📚 从本地数据库加载系列数据...")
            series_list = session.query(Series).all()
            self._series_data = [series.to_dict() for series in series_list]
            print(f"✅ 系列数据加载完成: {len(self._series_data)} 个")

            # 2. 加载分类数据
            print("📂 从本地数据库加载分类数据...")
            category_list = session.query(Category).all()
            self._category_data = [category.to_dict() for category in category_list]
            print(f"✅ 分类数据加载完成: {len(self._category_data)} 个")

            # 3. 加载视频数据
            print("📹 从本地数据库加载视频数据...")
            video_list = session.query(Video).all()
            self._video_data = [video.to_dict() for video in video_list]
            print(f"✅ 视频数据加载完成: {len(self._video_data)} 个")

            # 4. 加载用户数据
            print("👥 从本地数据库加载用户数据...")
            from database.models import User
            user_list = session.query(User).all()
            self._user_data = [user.to_dict() for user in user_list]
            print(f"✅ 用户数据加载完成: {len(self._user_data)} 个")

            session.close()

            self._data_loaded = True
            print("🎉 所有数据一次性加载完成！")
            print(f"📊 数据统计: 系列={len(self._series_data)}, 分类={len(self._category_data)}, 视频={len(self._video_data)}, 用户={len(self._user_data)}")
            
            # 计算并缓存统计数据
            self._statistics = self.calculate_statistics()
            print(f"📊 统计数据已缓存: {self._statistics}")
            
            return True

        except Exception as e:
            print(f"❌ 本地数据库加载异常: {e}")
            return False
    
    def calculate_series_price(self, series_id: str) -> float:
        """计算系列价格（所属分类价格之和）"""
        total_price = 0.0
        for category in self._category_data:
            if category.get('series_id') == series_id:
                total_price += category.get('price', 0.0)
        return total_price
    
    def calculate_total_price(self) -> float:
        """计算全套价格（所有分类价格之和）"""
        total_price = 0.0
        for category in self._category_data:
            total_price += category.get('price', 0.0)
        return total_price
    
    def get_series_list(self, page: int = 1, page_size: int = 20, 
                       search: Optional[str] = None) -> Dict[str, Any]:
        """从内存获取系列列表"""
        if not self._data_loaded:
            print("⚠️ 数据未加载，请先调用 load_all_data_once()")
            return {'success': False, 'message': '数据未加载', 'data': []}
        
        print(f"📚 从内存获取系列列表: page={page}, page_size={page_size}, search={search}")
        
        # 应用搜索筛选
        filtered_data = self._series_data.copy()  # 复制避免修改原数据
        if search:
            filtered_data = [s for s in filtered_data if search.lower() in s.get('title', '').lower()]
        
        # 动态计算每个系列的价格
        for series in filtered_data:
            series_id = series.get('id')
            series_title = series.get('title', '')
            
            if series_id:
                # 特殊处理：如果是"全套课程"系列，使用全套价格
                if series_title == "全套课程":
                    calculated_price = self.calculate_total_price()
                    print(f"💰 全套课程价格计算: ¥{calculated_price:.2f}")
                else:
                    calculated_price = self.calculate_series_price(series_id)
                
                series['calculated_price'] = calculated_price
                # 覆盖原价格，确保显示计算后的价格
                series['price'] = calculated_price
        
        # 应用分页
        total = len(filtered_data)
        start = (page - 1) * page_size
        end = start + page_size
        paginated_data = filtered_data[start:end]
        
        print(f"📚 内存系列数据: 总数={total}, 返回={len(paginated_data)}")
        
        return {
            'success': True,
            'data': paginated_data,
            'pagination': {
                'current_page': page,
                'page_size': page_size,
                'total_records': total,
                'total_pages': (total + page_size - 1) // page_size,
                'has_next': end < total,
                'has_prev': page > 1
            }
        }
    
    def get_category_list(self, page: int = 1, page_size: int = 20,
                         search: Optional[str] = None, series_id: Optional[str] = None) -> Dict[str, Any]:
        """从内存获取分类列表"""
        if not self._data_loaded:
            print("⚠️ 数据未加载，请先调用 load_all_data_once()")
            return {'success': False, 'message': '数据未加载', 'data': []}
        
        print(f"📂 从内存获取分类列表: page={page}, page_size={page_size}, search={search}, series_id={series_id}")
        
        # 应用筛选
        filtered_data = self._category_data
        if series_id:
            filtered_data = [c for c in filtered_data if c.get('series_id') == series_id]
        if search:
            filtered_data = [c for c in filtered_data if search.lower() in c.get('title', '').lower()]
        
        # 应用分页
        total = len(filtered_data)
        start = (page - 1) * page_size
        end = start + page_size
        paginated_data = filtered_data[start:end]
        
        print(f"📂 内存分类数据: 总数={total}, 返回={len(paginated_data)}")
        
        return {
            'success': True,
            'data': paginated_data,
            'pagination': {
                'current_page': page,
                'page_size': page_size,
                'total_records': total,
                'total_pages': (total + page_size - 1) // page_size,
                'has_next': end < total,
                'has_prev': page > 1
            }
        }
    
    def get_video_list(self, page: int = 1, page_size: int = 20,
                      search: Optional[str] = None, series_id: Optional[str] = None,
                      category_id: Optional[str] = None) -> Dict[str, Any]:
        """从内存获取视频列表"""
        if not self._data_loaded:
            print("⚠️ 数据未加载，请先调用 load_all_data_once()")
            return {'success': False, 'message': '数据未加载', 'data': []}

        print(f"📹 从内存获取视频列表: page={page}, page_size={page_size}, search={search}, series_id={series_id}, category_id={category_id}")

        # 应用筛选
        filtered_data = self._video_data
        if series_id:
            filtered_data = [v for v in filtered_data if v.get('series_id') == series_id]
        if category_id:
            filtered_data = [v for v in filtered_data if v.get('category_id') == category_id]
        if search:
            filtered_data = [v for v in filtered_data if search.lower() in v.get('title', '').lower()]

        # 应用分页
        total = len(filtered_data)
        start = (page - 1) * page_size
        end = start + page_size
        paginated_data = filtered_data[start:end]

        print(f"📹 内存视频数据: 总数={total}, 返回={len(paginated_data)}")

        return {
            'success': True,
            'data': paginated_data,
            'pagination': {
                'current_page': page,
                'page_size': page_size,
                'total_records': total,
                'total_pages': (total + page_size - 1) // page_size,
                'has_next': end < total,
                'has_prev': page > 1
            }
        }

    def get_category_detail(self, category_id: str) -> Dict[str, Any]:
        """从内存获取分类详情"""
        if not self._data_loaded:
            print("⚠️ 数据未加载，请先调用 load_all_data_once()")
            return {'success': False, 'message': '数据未加载', 'data': None}

        print(f"📂 从内存获取分类详情: category_id={category_id}")

        # 查找分类
        category = None
        for cat in self._category_data:
            if str(cat.get('id')) == str(category_id):
                category = cat.copy()  # 复制一份避免修改原数据
                break

        if not category:
            print(f"❌ 内存中未找到分类 {category_id}")
            return {
                'success': False,
                'message': '分类不存在',
                'data': None
            }

        # 获取分类下的视频
        category_videos = [v for v in self._video_data if str(v.get('category_id')) == str(category_id)]
        category['videos'] = category_videos

        print(f"✅ 从内存获取分类详情成功: {category.get('title')}, 视频数量: {len(category_videos)}")

        return {
            'success': True,
            'data': category,
            'message': '获取分类详情成功'
        }

    def get_video_detail(self, video_id: str) -> Dict[str, Any]:
        """从内存获取视频详情（修复关键点47）"""
        if not self._data_loaded:
            print("⚠️ 数据未加载，请先调用 load_all_data_once()")
            return {'success': False, 'message': '数据未加载', 'data': None}

        print(f"📹 从内存获取视频详情: video_id={video_id}")

        # 查找视频
        video = None
        for vid in self._video_data:
            if str(vid.get('id')) == str(video_id):
                video = vid.copy()  # 复制一份避免修改原数据
                break

        if not video:
            print(f"❌ 内存中未找到视频 {video_id}")
            return {
                'success': False,
                'message': '视频不存在',
                'data': None
            }

        # 获取视频所属的分类信息
        category_id = video.get('category_id')
        if category_id:
            category = self.get_category_by_id(str(category_id))
            if category:
                video['category_title'] = category.get('title', '未知分类')
                
                # 获取系列信息 - 确保系列信息正确
                series_id = category.get('series_id')
                if series_id:
                    # 确保视频的series_id字段与分类的series_id一致
                    video['series_id'] = series_id
                    series = self.get_series_by_id(str(series_id))
                    if series:
                        video['series_title'] = series.get('title', '未知系列')
                        print(f"✅ 系列信息已同步: {video['series_title']} (ID: {series_id})")
                    else:
                        print(f"⚠️ 未找到系列数据: {series_id}")
                        video['series_title'] = '未知系列'
                else:
                    print(f"⚠️ 分类缺少系列ID: {category_id}")
                    video['series_id'] = ''
                    video['series_title'] = '无关联系列'
            else:
                print(f"⚠️ 未找到分类数据: {category_id}")
                video['category_title'] = '未知分类'
                video['series_id'] = ''
                video['series_title'] = '无关联系列'
        else:
            print(f"⚠️ 视频缺少分类ID: {video_id}")
            video['category_title'] = '无关联分类'
            video['series_id'] = ''
            video['series_title'] = '无关联系列'

        print(f"✅ 从内存获取视频详情成功: {video.get('title')} - 系列: {video.get('series_title')}")

        return {
            'success': True,
            'data': video,
            'message': '获取视频详情成功'
        }

    def is_data_loaded(self) -> bool:
        """检查数据是否已加载"""
        return self._data_loaded
    
    def get_data_summary(self) -> Dict[str, int]:
        """获取数据统计"""
        return {
            'series_count': len(self._series_data),
            'category_count': len(self._category_data),
            'video_count': len(self._video_data),
            'loaded': self._data_loaded
        }

    def get_series_by_id(self, series_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取系列数据"""
        if not self._data_loaded:
            print("⚠️ 数据未加载，请先调用 load_all_data_once()")
            return None

        for series in self._series_data:
            if str(series.get('id')) == str(series_id):
                return series.copy()  # 返回副本避免修改原数据

        return None

    def get_video_by_id(self, video_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取视频数据"""
        if not self._data_loaded:
            print("⚠️ 数据未加载，请先调用 load_all_data_once()")
            return None

        for video in self._video_data:
            if str(video.get('id')) == str(video_id):
                return video.copy()  # 返回副本避免修改原数据

        return None

    def get_category_videos(self, category_id: str) -> List[Dict[str, Any]]:
        """获取分类下的所有视频"""
        if not self._data_loaded:
            print("⚠️ 数据未加载，请先调用 load_all_data_once()")
            return []

        category_videos = []
        for video in self._video_data:
            if str(video.get('category_id')) == str(category_id):
                category_videos.append(video.copy())  # 返回副本避免修改原数据

        return category_videos

    def get_category_by_id(self, category_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取分类数据"""
        if not self._data_loaded:
            print("⚠️ 数据未加载，请先调用 load_all_data_once()")
            return None

        for category in self._category_data:
            if str(category.get('id')) == str(category_id):
                return category.copy()  # 返回副本避免修改原数据

        return None

    def force_reload_data(self) -> bool:
        """强制重新加载所有数据（忽略缓存状态）"""
        print("🔄 强制重新加载所有数据...")

        # 重置加载状态
        self._data_loaded = False

        # 清空内存数据
        self._series_data.clear()
        self._category_data.clear()
        self._video_data.clear()

        # 重新加载数据
        return self.load_all_data_once()

    def clear_cache(self):
        """清除内存缓存"""
        print("🗑️ 清除内存缓存...")
        self._data_loaded = False
        self._series_data.clear()
        self._category_data.clear()
        self._video_data.clear()
        print("✅ 内存缓存已清除")

    def add_series_to_memory(self, series_data: dict):
        """添加新系列到内存缓存"""
        try:
            self._series_data.append(series_data)
            print(f"💾 新系列已添加到全局内存缓存: {series_data['title']}")
        except Exception as e:
            print(f"❌ 添加系列到全局内存缓存失败: {e}")

    def remove_series_from_memory(self, series_id: str):
        """从内存缓存中移除系列"""
        try:
            self._series_data = [s for s in self._series_data if s['id'] != series_id]
            print(f"🗑️ 系列已从全局内存缓存中移除: {series_id}")
        except Exception as e:
            print(f"❌ 从全局内存缓存移除系列失败: {e}")

    def add_category_to_memory(self, category_data: dict):
        """添加新分类到内存缓存"""
        try:
            self._category_data.append(category_data)
            print(f"💾 新分类已添加到全局内存缓存: {category_data['title']}")
        except Exception as e:
            print(f"❌ 添加分类到全局内存缓存失败: {e}")

    def remove_category_from_memory(self, category_id: str):
        """从内存缓存中移除分类"""
        try:
            self._category_data = [c for c in self._category_data if c['id'] != category_id]
            print(f"🗑️ 分类已从全局内存缓存中移除: {category_id}")
        except Exception as e:
            print(f"❌ 从全局内存缓存移除分类失败: {e}")

    def update_series_in_memory(self, series_id: str, series_data: Dict[str, Any]) -> bool:
        """更新内存中的系列数据"""
        try:
            for i, series in enumerate(self._series_data):
                if str(series.get('id')) == str(series_id):
                    self._series_data[i].update(series_data)
                    print(f"✅ 内存中系列 {series_id} 数据已更新")
                    return True
            print(f"⚠️ 内存中未找到系列 {series_id}")
            return False
        except Exception as e:
            print(f"❌ 更新内存系列数据失败: {e}")
            return False

    def update_category_in_memory(self, category_id: str, category_data: Dict[str, Any]) -> bool:
        """更新内存中的分类数据"""
        try:
            for i, category in enumerate(self._category_data):
                if str(category.get('id')) == str(category_id):
                    self._category_data[i].update(category_data)
                    print(f"✅ 内存中分类 {category_id} 数据已更新")
                    return True
            print(f"⚠️ 内存中未找到分类 {category_id}")
            return False
        except Exception as e:
            print(f"❌ 更新内存分类数据失败: {e}")
            return False

    def update_video_in_memory(self, video_id: str, video_data: Dict[str, Any]) -> bool:
        """更新内存中的视频数据"""
        try:
            for i, video in enumerate(self._video_data):
                if str(video.get('id')) == str(video_id):
                    self._video_data[i].update(video_data)
                    print(f"✅ 内存中视频 {video_id} 数据已更新")
                    return True
            print(f"⚠️ 内存中未找到视频 {video_id}")
            return False
        except Exception as e:
            print(f"❌ 更新内存视频数据失败: {e}")
            return False

    def add_series_to_memory(self, series_data: Dict[str, Any]) -> bool:
        """添加系列到内存"""
        try:
            self._series_data.append(series_data)
            print(f"✅ 系列 {series_data.get('id')} 已添加到内存")
            
            # 🎯 关键修复：添加数据后重新计算统计信息
            self._statistics = self.calculate_statistics()
            
            return True
        except Exception as e:
            print(f"❌ 添加系列到内存失败: {e}")
            return False

    def add_category_to_memory(self, category_data: Dict[str, Any]) -> bool:
        """添加分类到内存"""
        try:
            self._category_data.append(category_data)
            print(f"✅ 分类 {category_data.get('id')} 已添加到内存")
            
            # 🎯 关键修复：添加数据后重新计算统计信息
            self._statistics = self.calculate_statistics()
            
            # 🎯 关键修复：更新关联系列的统计信息
            series_id = category_data.get('series_id')
            if series_id:
                self.recalculate_series_category_count(series_id)
                self.recalculate_series_price(series_id)
            
            return True
        except Exception as e:
            print(f"❌ 添加分类到内存失败: {e}")
            return False

    def add_video_to_memory(self, video_data: Dict[str, Any]) -> bool:
        """添加视频到内存"""
        try:
            self._video_data.append(video_data)
            print(f"✅ 视频 {video_data.get('id')} 已添加到内存")
            
            # 🎯 关键修复：添加数据后重新计算统计信息
            self._statistics = self.calculate_statistics()
            
            # 🎯 关键修复：更新关联分类和系列的统计信息
            category_id = video_data.get('category_id')
            series_id = video_data.get('series_id')
            
            if category_id:
                self.recalculate_category_video_count(category_id)
            
            if series_id:
                self.recalculate_series_video_count(series_id)
            
            return True
        except Exception as e:
            print(f"❌ 添加视频到内存失败: {e}")
            return False

    def add_video_to_cache(self, video_data: Dict[str, Any]) -> bool:
        """添加视频到内存缓存（兼容接口）"""
        return self.add_video_to_memory(video_data)

    def update_video_in_cache(self, video_id: str, video_data: Dict[str, Any]) -> bool:
        """更新内存中的视频缓存（兼容接口）"""
        return self.update_video_in_memory(video_id, video_data)

    def remove_video_from_cache(self, video_id: str) -> bool:
        """从内存缓存中删除视频（兼容接口）"""
        return self.remove_video_from_memory(video_id)

    def remove_series_from_memory(self, series_id: str) -> bool:
        """从内存中删除系列"""
        try:
            for i, series in enumerate(self._series_data):
                if str(series.get('id')) == str(series_id):
                    del self._series_data[i]
                    print(f"✅ 系列 {series_id} 已从内存中删除")
                    
                    # 🎯 关键修复：删除数据后重新计算统计信息
                    self._statistics = self.calculate_statistics()
                    
                    return True
            print(f"⚠️ 内存中未找到系列 {series_id}")
            return False
        except Exception as e:
            print(f"❌ 从内存删除系列失败: {e}")
            return False

    def remove_category_from_memory(self, category_id: str) -> bool:
        """从内存中删除分类"""
        try:
            series_id = None
            for i, category in enumerate(self._category_data):
                if str(category.get('id')) == str(category_id):
                    # 🎯 关键修复：记录分类所属的系列ID，用于后续统计更新
                    series_id = category.get('series_id')
                    del self._category_data[i]
                    print(f"✅ 分类 {category_id} 已从内存中删除")
                    
                    # 🎯 关键修复：删除数据后重新计算统计信息
                    self._statistics = self.calculate_statistics()
                    
                    # 🎯 关键修复：更新关联系列的统计信息
                    if series_id:
                        self.recalculate_series_category_count(series_id)
                        self.recalculate_series_price(series_id)
                        self.recalculate_series_video_count(series_id)
                    
                    return True
            print(f"⚠️ 内存中未找到分类 {category_id}")
            return False
        except Exception as e:
            print(f"❌ 从内存删除分类失败: {e}")
            return False

    def remove_video_from_memory(self, video_id: str) -> bool:
        """从内存中删除视频"""
        try:
            category_id = None
            series_id = None
            for i, video in enumerate(self._video_data):
                if str(video.get('id')) == str(video_id):
                    # 🎯 关键修复：记录视频所属的分类和系列ID，用于后续统计更新
                    category_id = video.get('category_id')
                    series_id = video.get('series_id')
                    del self._video_data[i]
                    print(f"✅ 视频 {video_id} 已从内存中删除")
                    
                    # 🎯 关键修复：删除数据后重新计算统计信息
                    self._statistics = self.calculate_statistics()
                    
                    # 🎯 关键修复：更新关联分类和系列的统计信息
                    if category_id:
                        self.recalculate_category_video_count(category_id)
                    
                    if series_id:
                        self.recalculate_series_video_count(series_id)
                    
                    return True
            print(f"⚠️ 内存中未找到视频 {video_id}")
            return False
        except Exception as e:
            print(f"❌ 从内存删除视频失败: {e}")
            return False

    def smart_delete_series(self, series_id: str, sync_manager=None) -> Dict[str, Any]:
        """智能删除系列（级联删除分类和视频）"""
        try:
            # 1. 查找相关分类和视频
            related_categories = [cat for cat in self._category_data if cat.get('series_id') == series_id]
            related_videos = []
            
            for category in related_categories:
                category_id = category.get('id')
                if category_id:
                    videos = [vid for vid in self._video_data if vid.get('category_id') == category_id]
                    related_videos.extend(videos)
            
            print(f"🗑️ 准备删除系列 {series_id}: {len(related_categories)} 个分类, {len(related_videos)} 个视频")
            
            # 2. 先删除视频（本地数据库 + 服务器）
            deleted_videos = []
            for video in related_videos:
                video_id = video.get('id')
                if video_id:
                    # 从内存删除
                    if self.remove_video_from_memory(video_id):
                        deleted_videos.append(video_id)
                        # 从本地数据库删除
                        self._delete_from_local_database('videos', video_id)
                        # 从服务器删除
                        if sync_manager:
                            sync_manager.async_delete_from_server('video', video_id)
            
            # 3. 再删除分类（本地数据库 + 服务器）
            deleted_categories = []
            for category in related_categories:
                category_id = category.get('id')
                if category_id:
                    # 从内存删除
                    if self.remove_category_from_memory(category_id):
                        deleted_categories.append(category_id)
                        # 从本地数据库删除
                        self._delete_from_local_database('categories', category_id)
                        # 从服务器删除
                        if sync_manager:
                            sync_manager.async_delete_from_server('category', category_id)
            
            # 4. 最后删除系列（本地数据库 + 服务器）
            series_deleted = self.remove_series_from_memory(series_id)
            if series_deleted:
                # 从本地数据库删除
                self._delete_from_local_database('series', series_id)
                # 从服务器删除
                if sync_manager:
                    sync_manager.async_delete_from_server('series', series_id)
            
            return {
                'success': series_deleted,
                'message': f'系列删除完成: 系列={1 if series_deleted else 0}, 分类={len(deleted_categories)}, 视频={len(deleted_videos)}',
                'deleted': {
                    'series': [series_id] if series_deleted else [],
                    'categories': deleted_categories,
                    'videos': deleted_videos
                }
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'删除系列失败: {str(e)}'
            }

    def smart_delete_series_cascade(self, series_id: str, sync_manager=None) -> Dict[str, Any]:
        """级联删除系列及其关联数据（别名方法）"""
        return self.smart_delete_series(series_id, sync_manager)

    def smart_delete_category(self, category_id: str, sync_manager=None) -> Dict[str, Any]:
        """智能删除分类（级联删除视频）"""
        try:
            # 1. 查找相关视频
            related_videos = [vid for vid in self._video_data if vid.get('category_id') == category_id]
            
            print(f"🗑️ 准备删除分类 {category_id}: {len(related_videos)} 个视频")
            
            # 2. 先删除视频
            deleted_videos = []
            for video in related_videos:
                video_id = video.get('id')
                if video_id:
                    if self.remove_video_from_memory(video_id):
                        deleted_videos.append(video_id)
                        if sync_manager:
                            sync_manager.async_delete_from_server('video', video_id)
            
            # 3. 再删除分类
            category_deleted = self.remove_category_from_memory(category_id)
            if category_deleted and sync_manager:
                sync_manager.async_delete_from_server('category', category_id)
            
            return {
                'success': category_deleted,
                'message': f'分类删除完成: 分类={1 if category_deleted else 0}, 视频={len(deleted_videos)}',
                'deleted': {
                    'categories': [category_id] if category_deleted else [],
                    'videos': deleted_videos
                }
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'删除分类失败: {str(e)}'
            }
    
    def _delete_from_local_database(self, table_name: str, record_id: str) -> bool:
        """从本地数据库删除记录"""
        try:
            from utils.config import Config
            import pymysql
            
            # 创建数据库连接
            config = Config()
            db_config = config.get_database_config()
            
            conn = pymysql.connect(
                host=db_config['host'],
                user=db_config['user'],
                password=db_config['password'],
                database=db_config['database'],
                charset=db_config['charset']
            )
            
            try:
                cursor = conn.cursor()
                
                # 直接删除记录
                cursor.execute(f"DELETE FROM {table_name} WHERE id = %s", (record_id,))
                affected_rows = cursor.rowcount
                conn.commit()
                
                if affected_rows > 0:
                    print(f"✅ 本地数据库删除成功: {table_name}.{record_id}")
                    return True
                else:
                    print(f"⚠️ 本地数据库中未找到记录: {table_name}.{record_id}")
                    return False
                
            finally:
                conn.close()
                
        except Exception as e:
            print(f"❌ 本地数据库删除失败 {table_name}.{record_id}: {e}")
            return False

    def get_backup_history(self, table_name: str, record_id: str = None) -> Dict[str, Any]:
        """获取备份历史"""
        try:
            from utils.config import Config
            import pymysql
            
            # 创建数据库连接
            config = Config()
            db_config = config.get_database_config()
            
            conn = pymysql.connect(
                host=db_config['host'],
                user=db_config['user'],
                password=db_config['password'],
                database=db_config['database'],
                charset=db_config['charset']
            )
            
            try:
                cursor = conn.cursor(pymysql.cursors.DictCursor)
                
                table_map = {
                    'series': 'backup_series',
                    'categories': 'backup_categories',
                    'videos': 'backup_videos'
                }
                
                if table_name not in table_map:
                    return {
                        'success': False,
                        'message': f'不支持的表名: {table_name}'
                    }
                
                backup_table = table_map[table_name]
                
                if record_id:
                    cursor.execute(f"""
                        SELECT id, backup_time, title 
                        FROM {backup_table} 
                        WHERE id = %s 
                        ORDER BY backup_time DESC
                    """, (record_id,))
                else:
                    cursor.execute(f"""
                        SELECT id, backup_time, title 
                        FROM {backup_table} 
                        ORDER BY backup_time DESC 
                        LIMIT 100
                    """)
                
                backups = cursor.fetchall()
                
                return {
                    'success': True,
                    'data': backups,
                    'count': len(backups)
                }
                
            finally:
                conn.close()
                
        except Exception as e:
            return {
                'success': False,
                'message': f'获取备份历史失败: {str(e)}'
            }

    def get_users(self, page: int = 1, page_size: int = 20, search: str = None, is_active: bool = None) -> Dict[str, Any]:
        """获取用户数据（从内存缓存）"""
        if not self._data_loaded:
            return {
                'success': False,
                'message': '数据尚未加载，请先调用 load_all_data_once()'
            }

        try:
            # 过滤数据
            filtered_users = self._user_data.copy()

            # 搜索过滤
            if search:
                search_lower = search.lower()
                filtered_users = [
                    user for user in filtered_users
                    if (search_lower in user.get('username', '').lower() or
                        search_lower in user.get('email', '').lower() or
                        search_lower in user.get('display_name', '').lower())
                ]

            # 状态过滤
            if is_active is not None:
                filtered_users = [
                    user for user in filtered_users
                    if bool(user.get('is_active', False)) == is_active
                ]

            # 分页处理
            total_records = len(filtered_users)
            total_pages = (total_records + page_size - 1) // page_size
            start_index = (page - 1) * page_size
            end_index = start_index + page_size
            page_data = filtered_users[start_index:end_index]

            return {
                'success': True,
                'data': page_data,
                'pagination': {
                    'current_page': page,
                    'page_size': page_size,
                    'total_records': total_records,
                    'total_pages': total_pages,
                    'has_next': page < total_pages,
                    'has_prev': page > 1
                }
            }

        except Exception as e:
            return {
                'success': False,
                'message': f'获取用户数据失败: {str(e)}'
            }

    def calculate_statistics(self) -> Dict[str, int]:
        """计算统计数据"""
        try:
            # 基础统计
            stats = {
                'user_count': len(self._user_data),
                'series_count': len(self._series_data),
                'video_count': len(self._video_data),
                'category_count': len(self._category_data),
                'order_count': 0  # 暂时为0，后续从订单数据计算
            }

            # 计算全套价格（从系列数据中计算）
            total_price = 0.0
            for series in self._series_data:
                if not series.get('isFree', False) and not series.get('isPackage', False):
                    total_price += float(series.get('price', 0))

            stats['total_price'] = int(total_price)  # 转换为整数（分）

            return stats

        except Exception as e:
            print(f"❌ 统计数据计算失败: {e}")
            return {
                'user_count': 0,
                'series_count': 0,
                'video_count': 0,
                'category_count': 0,
                'order_count': 0,
                'total_price': 0
            }

    def get_statistics(self) -> Dict[str, int]:
        """获取统计数据（从缓存）"""
        if not self._data_loaded:
            return {
                'user_count': 0,
                'series_count': 0,
                'video_count': 0,
                'category_count': 0,
                'order_count': 0,
                'total_price': 0
            }

        return self._statistics.copy()
    
    
    def get_entity_by_id(self, entity_id: str, entity_type: str = None) -> Optional[Dict[str, Any]]:
        """
        根据ID获取实体
        
        Args:
            entity_id: 实体ID
            entity_type: 实体类型 ('series', 'category', 'video')，如果不指定则搜索所有类型
            
        Returns:
            Optional[Dict[str, Any]]: 找到的实体数据
        """
        try:
            if entity_type == 'series' or entity_type is None:
                for series in self._series_data:
                    if series.get('id') == entity_id:
                        return series
            
            if entity_type == 'category' or entity_type is None:
                for category in self._category_data:
                    if category.get('id') == entity_id:
                        return category
            
            if entity_type == 'video' or entity_type is None:
                for video in self._video_data:
                    if video.get('id') == entity_id:
                        return video
            
            return None
            
        except Exception as e:
            print(f"❌ 根据ID查找实体失败: {entity_id} - {e}")
            return None
    
    

    def get_category_videos(self, category_id: str) -> List[Dict]:
        """获取指定分类下的所有视频"""
        try:
            # 🎯 修复属性名：_video_data 而不是 _videos_data
            if not self._video_data:
                return []

            category_videos = [
                video for video in self._video_data
                if video.get('category_id') == category_id
            ]

            logger.info(f"获取分类 {category_id} 下的视频: {len(category_videos)} 个")
            return category_videos

        except Exception as e:
            logger.error(f"获取分类视频失败: {e}")
            return []

    def update_video_series_in_memory(self, video_id: str, new_series_id: str) -> bool:
        """在内存中更新视频的系列信息"""
        try:
            # 🎯 修复属性名：_video_data 而不是 _videos_data
            if not self._video_data:
                return False

            # 🎯 关键修复：获取新系列的标题
            new_series_title = None
            if self._series_data:
                for series in self._series_data:
                    if series.get('id') == new_series_id:
                        new_series_title = series.get('title', '未知系列')
                        break

            if new_series_title is None:
                logger.warning(f"未找到系列: {new_series_id}")
                new_series_title = '未知系列'

            for video in self._video_data:
                if video.get('id') == video_id:
                    old_series_id = video.get('series_id')
                    old_series_title = video.get('series_title')

                    # 🎯 关键修复：同时更新series_id和series_title
                    video['series_id'] = new_series_id
                    video['series_title'] = new_series_title

                    logger.info(f"视频 {video_id} 系列信息已更新:")
                    logger.info(f"  series_id: {old_series_id} → {new_series_id}")
                    logger.info(f"  series_title: {old_series_title} → {new_series_title}")
                    return True

            logger.warning(f"未找到视频: {video_id}")
            return False

        except Exception as e:
            logger.error(f"更新视频系列信息失败: {e}")
            return False

    def recalculate_series_category_count(self, series_id: str) -> bool:
        """重新计算系列的分类数量"""
        try:
            # 🎯 修复属性名：_category_data 和 _series_data
            if not self._category_data or not self._series_data:
                return False

            # 计算该系列下的分类数量
            category_count = len([
                category for category in self._category_data
                if category.get('series_id') == series_id
            ])

            # 更新系列数据中的分类数量
            for series in self._series_data:
                if series.get('id') == series_id:
                    old_count = series.get('category_count', 0)
                    series['category_count'] = category_count
                    logger.info(f"系列 {series_id} 分类数量已更新: {old_count} → {category_count}")
                    return True

            logger.warning(f"未找到系列: {series_id}")
            return False

        except Exception as e:
            logger.error(f"重新计算系列分类数量失败: {e}")
            return False

    def recalculate_category_video_count(self, category_id: str) -> bool:
        """重新计算分类的视频数量"""
        try:
            # 🎯 修复属性名：_video_data 和 _category_data
            if not self._video_data or not self._category_data:
                return False

            # 计算该分类下的视频数量
            video_count = len([
                video for video in self._video_data
                if video.get('category_id') == category_id
            ])

            # 更新分类数据中的视频数量
            for category in self._category_data:
                if category.get('id') == category_id:
                    old_count = category.get('video_count', 0)
                    category['video_count'] = video_count
                    logger.info(f"分类 {category_id} 视频数量已更新: {old_count} → {video_count}")
                    return True

            logger.warning(f"未找到分类: {category_id}")
            return False

        except Exception as e:
            logger.error(f"重新计算分类视频数量失败: {e}")
            return False

    def recalculate_series_video_count(self, series_id: str) -> bool:
        """重新计算系列的视频数量"""
        try:
            if not self._video_data or not self._series_data:
                return False

            # 计算该系列下的视频数量
            video_count = len([
                video for video in self._video_data
                if video.get('series_id') == series_id
            ])

            # 更新系列数据中的视频数量
            for series in self._series_data:
                if series.get('id') == series_id:
                    old_count = series.get('video_count', 0)
                    series['video_count'] = video_count
                    logger.info(f"系列 {series_id} 视频数量已更新: {old_count} → {video_count}")
                    return True

            logger.warning(f"未找到系列: {series_id}")
            return False

        except Exception as e:
            logger.error(f"重新计算系列视频数量失败: {e}")
            return False

    def recalculate_series_price(self, series_id: str) -> bool:
        """重新计算系列的价格（基于分类价格之和）"""
        try:
            if not self._category_data or not self._series_data:
                return False

            # 计算该系列下所有分类的价格总和
            total_price = sum(
                float(category.get('price', 0)) for category in self._category_data
                if category.get('series_id') == series_id
            )

            # 更新系列数据中的价格
            for series in self._series_data:
                if series.get('id') == series_id:
                    old_price = series.get('price', 0)
                    series['price'] = total_price
                    logger.info(f"系列 {series_id} 价格已更新: {old_price} → {total_price}")
                    return True

            logger.warning(f"未找到系列: {series_id}")
            return False

        except Exception as e:
            logger.error(f"重新计算系列价格失败: {e}")
            return False

    def refresh_series_statistics(self, series_id: str) -> bool:
        """刷新系列的所有统计信息（分类数、视频数、价格）"""
        try:
            success_count = 0
            
            # 重新计算分类数
            if self.recalculate_series_category_count(series_id):
                success_count += 1
            
            # 重新计算视频数
            if self.recalculate_series_video_count(series_id):
                success_count += 1
                
            # 重新计算价格
            if self.recalculate_series_price(series_id):
                success_count += 1
            
            # 重新计算全局统计
            self._statistics = self.calculate_statistics()
            
            logger.info(f"系列 {series_id} 统计信息刷新完成，成功更新 {success_count}/3 项")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"刷新系列统计信息失败: {e}")
            return False

    def refresh_category_statistics(self, category_id: str) -> bool:
        """刷新分类的统计信息（视频数）"""
        try:
            # 重新计算视频数
            success = self.recalculate_category_video_count(category_id)
            
            # 如果分类属于某个系列，也更新系列的统计信息
            if success:
                for category in self._category_data:
                    if category.get('id') == category_id:
                        series_id = category.get('series_id')
                        if series_id:
                            self.recalculate_series_video_count(series_id)
                        break
            
            # 重新计算全局统计
            self._statistics = self.calculate_statistics()
            
            logger.info(f"分类 {category_id} 统计信息刷新完成")
            return success
            
        except Exception as e:
            logger.error(f"刷新分类统计信息失败: {e}")
            return False

    def get_series_detail(self, series_id: str) -> Dict[str, Any]:
        """获取系列详情"""
        try:
            if not self._series_data:
                return {
                    'success': False,
                    'message': '系列数据未加载',
                    'data': None
                }

            # 查找指定系列
            for series in self._series_data:
                if series.get('id') == series_id:
                    logger.info(f"获取系列详情成功: {series.get('title', '未知系列')}")
                    return {
                        'success': True,
                        'message': '获取系列详情成功',
                        'data': series
                    }

            logger.warning(f"未找到系列: {series_id}")
            return {
                'success': False,
                'message': f'未找到系列: {series_id}',
                'data': None
            }

        except Exception as e:
            logger.error(f"获取系列详情失败: {e}")
            return {
                'success': False,
                'message': f'获取系列详情失败: {str(e)}',
                'data': None
            }
    
    # 🏢 统一级联更新接口 - 四层同步乐观更新架构
    
    def cascade_update_manager(self, operation_type: str, entity_type: str, entity_id: str, data: Dict[str, Any] = None, sync_manager=None) -> Dict[str, Any]:
        """
        统一级联更新管理器
        
        Args:
            operation_type: 操作类型 ('create', 'update', 'delete')
            entity_type: 实体类型 ('series', 'category', 'video')
            entity_id: 实体ID
            data: 操作数据（创建/更新时需要）
            sync_manager: 同步管理器
            
        Returns:
            Dict[str, Any]: 操作结果
        """
        try:
            logger.info(f"🏢 级联更新管理器: {operation_type} {entity_type} {entity_id}")
            
            if operation_type == 'create':
                return self._handle_cascade_create(entity_type, entity_id, data, sync_manager)
            elif operation_type == 'update':
                return self._handle_cascade_update(entity_type, entity_id, data, sync_manager)
            elif operation_type == 'delete':
                return self._handle_cascade_delete(entity_type, entity_id, sync_manager)
            else:
                return {
                    'success': False,
                    'message': f'不支持的操作类型: {operation_type}'
                }
                
        except Exception as e:
            logger.error(f"级联更新管理器失败: {e}")
            return {
                'success': False,
                'message': f'级联更新失败: {str(e)}'
            }
    
    def _handle_cascade_create(self, entity_type: str, entity_id: str, data: Dict[str, Any], sync_manager=None) -> Dict[str, Any]:
        """处理级联创建操作"""
        try:
            logger.info(f"🎆 处理级联创建: {entity_type} {entity_id}")
            
            # 添加到内存缓存
            if entity_type == 'series':
                success = self.add_series_to_memory(data)
                affected_entities = {'series': [entity_id]}
            elif entity_type == 'category':
                success = self.add_category_to_memory(data)
                # 更新关联系列的统计信息
                series_id = data.get('series_id')
                affected_entities = {'categories': [entity_id]}
                if series_id:
                    self.refresh_series_statistics(series_id)
                    affected_entities['series'] = [series_id]
            elif entity_type == 'video':
                success = self.add_video_to_memory(data)
                # 更新关联分类和系列的统计信息
                category_id = data.get('category_id')
                series_id = data.get('series_id')
                affected_entities = {'videos': [entity_id]}
                if category_id:
                    self.refresh_category_statistics(category_id)
                    affected_entities['categories'] = [category_id]
                if series_id:
                    self.refresh_series_statistics(series_id)
                    affected_entities['series'] = [series_id]
            else:
                return {
                    'success': False,
                    'message': f'不支持的实体类型: {entity_type}'
                }
            
            return {
                'success': success,
                'message': f'{entity_type}创建完成',
                'affected': affected_entities
            }
            
        except Exception as e:
            logger.error(f"级联创建失败: {e}")
            return {
                'success': False,
                'message': f'级联创建失败: {str(e)}'
            }
    
    def _handle_cascade_update(self, entity_type: str, entity_id: str, data: Dict[str, Any], sync_manager=None) -> Dict[str, Any]:
        """处理级联更新操作"""
        try:
            logger.info(f"🔄 处理级联更新: {entity_type} {entity_id}")
            
            affected_entities = {}
            
            # 更新内存数据
            if entity_type == 'series':
                success = self.update_series_in_memory(entity_id, data)
                affected_entities['series'] = [entity_id]
                # 系列更新可能影响所有关联分类和视频
                self._update_related_entities_after_series_update(entity_id, affected_entities)
            elif entity_type == 'category':
                # 获取更新前的数据，检查是否变更了系列
                old_data = self.get_category_by_id(entity_id)
                success = self.update_category_in_memory(entity_id, data)
                affected_entities['categories'] = [entity_id]
                
                # 检查系列关联变更
                old_series_id = old_data.get('series_id') if old_data else None
                new_series_id = data.get('series_id')
                
                if old_series_id != new_series_id:
                    # 系列关联发生变更，需要更新两个系列的统计
                    if old_series_id:
                        self.refresh_series_statistics(old_series_id)
                        affected_entities.setdefault('series', []).append(old_series_id)
                    if new_series_id:
                        self.refresh_series_statistics(new_series_id)
                        affected_entities.setdefault('series', []).append(new_series_id)
                else:
                    # 系列未变更，只更新当前系列的统计
                    if new_series_id:
                        self.refresh_series_statistics(new_series_id)
                        affected_entities.setdefault('series', []).append(new_series_id)
                
                # 更新关联视频的系列信息
                self._update_related_videos_after_category_update(entity_id, data, affected_entities)
                
            elif entity_type == 'video':
                # 获取更新前的数据
                old_data = self.get_video_by_id(entity_id)
                success = self.update_video_in_memory(entity_id, data)
                affected_entities['videos'] = [entity_id]
                
                # 检查分类和系列关联变更
                old_category_id = old_data.get('category_id') if old_data else None
                old_series_id = old_data.get('series_id') if old_data else None
                new_category_id = data.get('category_id')
                new_series_id = data.get('series_id')
                
                # 更新影响的分类统计
                if old_category_id != new_category_id:
                    if old_category_id:
                        self.refresh_category_statistics(old_category_id)
                        affected_entities.setdefault('categories', []).append(old_category_id)
                    if new_category_id:
                        self.refresh_category_statistics(new_category_id)
                        affected_entities.setdefault('categories', []).append(new_category_id)
                elif new_category_id:
                    self.refresh_category_statistics(new_category_id)
                    affected_entities.setdefault('categories', []).append(new_category_id)
                
                # 更新影响的系列统计
                if old_series_id != new_series_id:
                    if old_series_id:
                        self.refresh_series_statistics(old_series_id)
                        affected_entities.setdefault('series', []).append(old_series_id)
                    if new_series_id:
                        self.refresh_series_statistics(new_series_id)
                        affected_entities.setdefault('series', []).append(new_series_id)
                elif new_series_id:
                    self.refresh_series_statistics(new_series_id)
                    affected_entities.setdefault('series', []).append(new_series_id)
            else:
                return {
                    'success': False,
                    'message': f'不支持的实体类型: {entity_type}'
                }
            
            return {
                'success': success,
                'message': f'{entity_type}更新完成',
                'affected': affected_entities
            }
            
        except Exception as e:
            logger.error(f"级联更新失败: {e}")
            return {
                'success': False,
                'message': f'级联更新失败: {str(e)}'
            }
    
    def _handle_cascade_delete(self, entity_type: str, entity_id: str, sync_manager=None) -> Dict[str, Any]:
        """处理级联删除操作"""
        try:
            logger.info(f"🗑️ 处理级联删除: {entity_type} {entity_id}")
            
            # 使用现有的级联删除方法
            if entity_type == 'series':
                return self.smart_delete_series(entity_id, sync_manager)
            elif entity_type == 'category':
                return self.smart_delete_category(entity_id, sync_manager)
            elif entity_type == 'video':
                # 视频删除不需要级联，只需要更新关联统计
                old_data = self.get_video_by_id(entity_id)
                success = self.remove_video_from_memory(entity_id)
                
                affected_entities = {'videos': [entity_id]}
                if old_data:
                    category_id = old_data.get('category_id')
                    series_id = old_data.get('series_id')
                    if category_id:
                        affected_entities['categories'] = [category_id]
                    if series_id:
                        affected_entities['series'] = [series_id]
                
                return {
                    'success': success,
                    'message': f'视频删除完成',
                    'deleted': affected_entities
                }
            else:
                return {
                    'success': False,
                    'message': f'不支持的实体类型: {entity_type}'
                }
                
        except Exception as e:
            logger.error(f"级联删除失败: {e}")
            return {
                'success': False,
                'message': f'级联删除失败: {str(e)}'
            }
    
    def _update_related_entities_after_series_update(self, series_id: str, affected_entities: Dict[str, list]):
        """系列更新后更新关联实体"""
        try:
            # 更新所有属于该系列的分类
            related_categories = [cat for cat in self._category_data if cat.get('series_id') == series_id]
            if related_categories:
                category_ids = [cat.get('id') for cat in related_categories]
                affected_entities.setdefault('categories', []).extend(category_ids)
            
            # 更新所有属于该系列的视频
            related_videos = [vid for vid in self._video_data if vid.get('series_id') == series_id]
            if related_videos:
                video_ids = [vid.get('id') for vid in related_videos]
                affected_entities.setdefault('videos', []).extend(video_ids)
            
            logger.info(f"系列更新影响: 分类{len(related_categories)}个, 视频{len(related_videos)}个")
            
        except Exception as e:
            logger.error(f"更新系列关联实体失败: {e}")
    
    def _update_related_videos_after_category_update(self, category_id: str, category_data: Dict[str, Any], affected_entities: Dict[str, list]):
        """分类更新后更新关联视频"""
        try:
            # 更新所有属于该分类的视频的系列信息
            new_series_id = category_data.get('series_id')
            related_videos = [vid for vid in self._video_data if vid.get('category_id') == category_id]
            
            for video in related_videos:
                old_series_id = video.get('series_id')
                if old_series_id != new_series_id:
                    # 更新视频的系列信息
                    video_id = video.get('id')
                    if video_id and new_series_id:
                        self.update_video_series_in_memory(video_id, new_series_id)
                        affected_entities.setdefault('videos', []).append(video_id)
            
            logger.info(f"分类更新影响视频: {len(related_videos)}个")
            
        except Exception as e:
            logger.error(f"更新分类关联视频失败: {e}")

    def _load_users_from_database(self):
        """从本地数据库加载用户数据（降级机制）"""
        try:
            from database.models import DatabaseManager, User
            from utils.config import Config

            print("💾 开始从本地数据库加载用户数据...")
            
            # 创建数据库连接
            config = Config()
            db_config = config.get_database_config()
            connection_string = f"mysql+pymysql://{db_config['user']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['database']}?charset={db_config['charset']}"
            db_manager = DatabaseManager(connection_string)
            session = db_manager.get_session()

            try:
                # 从本地数据库加载用户数据
                user_list = session.query(User).all()
                self._user_data = [user.to_dict() for user in user_list]
                print(f"✅ 从本地数据库加载用户数据完成: {len(self._user_data)} 个")
                
                if len(self._user_data) == 0:
                    print("⚠️ 本地数据库中没有用户数据，尝试创建示例数据...")
                    self._create_sample_user_data(session)
                    
            finally:
                session.close()

        except Exception as e:
            print(f"❌ 从本地数据库加载用户数据失败: {e}")
            # 最后的降级：创建示例用户数据
            self._create_fallback_user_data()

    def _create_fallback_user_data(self):
        """创建备用的示例用户数据"""
        print("🔧 创建备用示例用户数据...")
        self._user_data = [
            {
                'user_id': 'user_001',
                'id': 'user_001',
                'username': 'admin',
                'display_name': '管理员',
                'email': '<EMAIL>',
                'is_active': True,
                'is_admin': True
            },
            {
                'user_id': 'user_002', 
                'id': 'user_002',
                'username': 'mike',
                'display_name': 'Mike',
                'email': '<EMAIL>',
                'is_active': True,
                'is_admin': False
            },
            {
                'user_id': 'user_003',
                'id': 'user_003', 
                'username': 'test_user1',
                'display_name': '测试用户1',
                'email': '<EMAIL>',
                'is_active': True,
                'is_admin': False
            },
            {
                'user_id': 'user_004',
                'id': 'user_004',
                'username': 'test_user2', 
                'display_name': '测试用户2',
                'email': '<EMAIL>',
                'is_active': True,
                'is_admin': False
            },
            {
                'user_id': 'user_005',
                'id': 'user_005',
                'username': 'demo_user',
                'display_name': '演示用户',
                'email': '<EMAIL>',
                'is_active': False,
                'is_admin': False
            }
        ]
        print(f"✅ 备用用户数据创建完成: {len(self._user_data)} 个")

    def _create_sample_user_data(self, session):
        """在数据库中创建示例用户数据"""
        try:
            from database.models import User
            
            sample_users = [
                User(
                    id='user_001',
                    username='admin',
                    display_name='管理员',
                    email='<EMAIL>',
                    password_hash='admin_hash',
                    is_active=True,
                    is_admin=True
                ),
                User(
                    id='user_002',
                    username='mike', 
                    display_name='Mike',
                    email='<EMAIL>',
                    password_hash='mike_hash',
                    is_active=True,
                    is_admin=False
                )
            ]
            
            for user in sample_users:
                session.merge(user)  # 使用merge避免重复
            
            session.commit()
            print("✅ 示例用户数据已添加到数据库")
            
            # 重新加载用户数据
            user_list = session.query(User).all()
            self._user_data = [user.to_dict() for user in user_list]
            
        except Exception as e:
            print(f"❌ 创建示例用户数据失败: {e}")
            session.rollback()
            # 使用备用数据
            self._create_fallback_user_data()

# 全局单例实例
global_data_manager = GlobalDataManager()
