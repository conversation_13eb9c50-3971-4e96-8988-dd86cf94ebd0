#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户业务服务
"""

import re
import hashlib
from typing import List, Optional, Dict, Any, Tuple
from database.dao import UserDAO
from database.models import DatabaseManager
from cache.data_manager import DataManager
import logging

logger = logging.getLogger(__name__)

class UserService:
    """用户业务服务类"""

    def __init__(self, db_manager: DatabaseManager, use_api: bool = False, use_cache: bool = True):
        self.db_manager = db_manager
        self.use_api = use_api
        self.use_cache = use_cache

        # 初始化API客户端
        if self.use_api:
            from utils.api_client import APIClient
            self.api_client = APIClient()
            logger.info("用户服务使用API模式")

        # 初始化数据管理器
        if self.use_cache:
            self.data_manager = DataManager()
            logger.info("用户服务使用缓存优先模式")
        else:
            self.data_manager = None
    
    def get_user_list(self, page: int = 1, page_size: int = 20,
                     search: str = None, is_active: bool = None) -> Dict[str, Any]:
        """获取用户列表"""
        try:
            # 优先使用全局缓存数据
            try:
                from cache.global_data_manager import global_data_manager
                if global_data_manager._data_loaded:
                    logger.info("使用全局缓存获取用户列表")
                    return global_data_manager.get_users(page=page, page_size=page_size,
                                                       search=search, is_active=is_active)
            except Exception as e:
                logger.warning(f"全局缓存获取用户失败: {e}")

            # 备选：使用本地缓存数据
            if self.use_cache and self.data_manager and self.data_manager._initialized:
                logger.info("使用本地缓存获取用户列表")
                return self.data_manager.get_data('users', page=page, page_size=page_size,
                                                search=search, is_active=is_active)

            # 使用API模式
            if self.use_api:
                logger.info("使用API获取用户列表")
                params = {
                    'page': page,
                    'page_size': page_size
                }
                if search:
                    params['search'] = search
                if is_active is not None:
                    params['is_active'] = is_active

                response = self.api_client.get('/api/admin/v1/users', params=params)
                if response and response.get('success'):
                    # 转换分页格式以匹配预期
                    if 'pagination' in response:
                        pagination = response['pagination']
                        # 转换API返回的分页格式
                        response['pagination'] = {
                            'current_page': pagination.get('page', page),
                            'page_size': pagination.get('page_size', page_size),
                            'total_records': pagination.get('total', 0),
                            'total_pages': pagination.get('pages', 0),
                            'has_next': pagination.get('page', page) < pagination.get('pages', 0),
                            'has_prev': pagination.get('page', page) > 1
                        }
                    return response
                else:
                    logger.error(f"API获取用户列表失败: {response}")
                    return {'success': False, 'message': 'API获取用户列表失败', 'data': [], 'pagination': {}}

            # 使用本地数据库模式
            session = self.db_manager.get_session()
            user_dao = UserDAO(session)

            users, total = user_dao.get_all(page, page_size, search, is_active)

            # 转换为字典格式
            user_list = [user.to_dict() for user in users]

            # 计算分页信息
            total_pages = (total + page_size - 1) // page_size

            session.close()

            return {
                'success': True,
                'data': user_list,
                'pagination': {
                    'current_page': page,
                    'page_size': page_size,
                    'total_records': total,
                    'total_pages': total_pages,
                    'has_next': page < total_pages,
                    'has_prev': page > 1
                }
            }
        except Exception as e:
            logger.error(f"获取用户列表失败: {e}")
            return {
                'success': False,
                'message': f'获取用户列表失败: {str(e)}',
                'data': [],
                'pagination': {}
            }
    
    def get_user_detail(self, user_id: int) -> Dict[str, Any]:
        """获取用户详情"""
        try:
            session = self.db_manager.get_session()
            user_dao = UserDAO(session)
            
            user = user_dao.get_by_id(user_id)
            if not user:
                return {
                    'success': False,
                    'message': '用户不存在',
                    'data': None
                }
            
            # 获取用户订单
            orders = user_dao.get_user_orders(user_id)
            order_list = [order.to_dict() for order in orders]
            
            user_data = user.to_dict()
            user_data['orders'] = order_list
            user_data['order_count'] = len(order_list)
            
            # 计算总消费金额
            total_spent = sum(float(order.amount) for order in orders if order.status == 'completed')
            user_data['total_spent'] = total_spent
            
            session.close()
            
            return {
                'success': True,
                'data': user_data,
                'message': '获取用户详情成功'
            }
        except Exception as e:
            logger.error(f"获取用户详情失败: {e}")
            return {
                'success': False,
                'message': f'获取用户详情失败: {str(e)}',
                'data': None
            }
    
    def create_user(self, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建用户"""
        try:
            # 数据验证
            validation_result = self._validate_user_data(user_data)
            if not validation_result['valid']:
                return {
                    'success': False,
                    'message': validation_result['message'],
                    'data': None
                }
            
            session = self.db_manager.get_session()
            user_dao = UserDAO(session)
            
            # 检查用户名是否已存在
            existing_user = user_dao.get_by_username(user_data['username'])
            if existing_user:
                session.close()
                return {
                    'success': False,
                    'message': '用户名已存在',
                    'data': None
                }
            
            # 检查邮箱是否已存在
            existing_email = user_dao.get_by_email(user_data['email'])
            if existing_email:
                session.close()
                return {
                    'success': False,
                    'message': '邮箱已存在',
                    'data': None
                }
            
            # 🎯 修复：保存明文密码，不进行哈希处理
            if 'password' in user_data:
                user_data['password_hash'] = user_data['password']  # 直接保存明文
                del user_data['password']
            
            # 创建用户
            user = user_dao.create(user_data)

            # 在关闭session之前获取用户数据
            user_dict = user.to_dict()

            session.close()

            return {
                'success': True,
                'message': '用户创建成功',
                'data': user_dict
            }
        except Exception as e:
            logger.error(f"创建用户失败: {e}")
            return {
                'success': False,
                'message': f'创建用户失败: {str(e)}',
                'data': None
            }
    
    def update_user(self, user_id: int, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新用户"""
        try:
            # 数据验证
            validation_result = self._validate_user_data(user_data, is_update=True)
            if not validation_result['valid']:
                return {
                    'success': False,
                    'message': validation_result['message'],
                    'data': None
                }
            
            session = self.db_manager.get_session()
            user_dao = UserDAO(session)
            
            # 检查用户是否存在
            existing_user = user_dao.get_by_id(user_id)
            if not existing_user:
                session.close()
                return {
                    'success': False,
                    'message': '用户不存在',
                    'data': None
                }
            
            # 检查用户名是否被其他用户使用
            if 'username' in user_data:
                username_user = user_dao.get_by_username(user_data['username'])
                if username_user and username_user.id != user_id:
                    session.close()
                    return {
                        'success': False,
                        'message': '用户名已被其他用户使用',
                        'data': None
                    }
            
            # 检查邮箱是否被其他用户使用
            if 'email' in user_data:
                email_user = user_dao.get_by_email(user_data['email'])
                if email_user and email_user.id != user_id:
                    session.close()
                    return {
                        'success': False,
                        'message': '邮箱已被其他用户使用',
                        'data': None
                    }
            
            # 🎯 修复：保存明文密码，不进行哈希处理
            if 'password' in user_data:
                user_data['password_hash'] = user_data['password']  # 直接保存明文
                del user_data['password']
            
            # 更新用户
            user = user_dao.update(user_id, user_data)
            
            session.close()
            
            return {
                'success': True,
                'message': '用户更新成功',
                'data': user.to_dict() if user else None
            }
        except Exception as e:
            logger.error(f"更新用户失败: {e}")
            return {
                'success': False,
                'message': f'更新用户失败: {str(e)}',
                'data': None
            }
    
    def delete_user(self, user_id, hard_delete: bool = False) -> Dict[str, Any]:
        """删除用户 - 支持UUID字符串和API模式"""
        try:
            # 🎯 关键修复：使用API模式
            if self.use_api:
                logger.info(f"使用API删除用户: {user_id}")
                response = self.api_client.delete(f'/api/admin/v1/users/{user_id}')
                if response and response.get('success'):
                    # 清理全局缓存中的用户数据
                    self.remove_user_from_cache(user_id)
                    return response
                else:
                    logger.error(f"API删除用户失败: {response}")
                    return {'success': False, 'message': response.get('message', 'API删除用户失败')}
            
            # 使用本地数据库模式
            session = self.db_manager.get_session()
            user_dao = UserDAO(session)
            
            # 🎯 修复：直接使用get_by_id，因为UserDAO没有get_by_user_id方法
            user = user_dao.get_by_id(user_id)
                
            if not user:
                session.close()
                return {
                    'success': False,
                    'message': '用户不存在'
                }
            
            # 执行删除
            if hard_delete:
                success = user_dao.hard_delete(user.id)  # 使用数据库主键
                message = '用户删除成功' if success else '用户删除失败'
            else:
                success = user_dao.delete(user.id)  # 使用数据库主键
                message = '用户禁用成功' if success else '用户禁用失败'
            
            session.close()
            
            return {
                'success': success,
                'message': message
            }
        except Exception as e:
            logger.error(f"删除用户失败: {e}")
            return {
                'success': False,
                'message': f'删除用户失败: {str(e)}'
            }
    
    def get_user_statistics(self) -> Dict[str, Any]:
        """获取用户统计信息"""
        try:
            session = self.db_manager.get_session()
            user_dao = UserDAO(session)
            
            stats = user_dao.get_statistics()
            
            session.close()
            
            return {
                'success': True,
                'data': stats,
                'message': '获取统计信息成功'
            }
        except Exception as e:
            logger.error(f"获取用户统计失败: {e}")
            return {
                'success': False,
                'message': f'获取用户统计失败: {str(e)}',
                'data': {}
            }
    
    def _validate_user_data(self, user_data: Dict[str, Any], is_update: bool = False) -> Dict[str, Any]:
        """验证用户数据"""
        if not is_update:
            # 创建时必填字段
            required_fields = ['username', 'email']
            for field in required_fields:
                if field not in user_data or not user_data[field]:
                    return {
                        'valid': False,
                        'message': f'{field} 是必填字段'
                    }
        
        # 用户名验证
        if 'username' in user_data:
            username = user_data['username']
            if len(username) < 3 or len(username) > 50:
                return {
                    'valid': False,
                    'message': '用户名长度必须在3-50个字符之间'
                }
            
            if not re.match(r'^[a-zA-Z0-9_]+$', username):
                return {
                    'valid': False,
                    'message': '用户名只能包含字母、数字和下划线'
                }
        
        # 邮箱验证
        if 'email' in user_data:
            email = user_data['email']
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            if not re.match(email_pattern, email):
                return {
                    'valid': False,
                    'message': '邮箱格式不正确'
                }
        
        # 密码验证
        if 'password' in user_data:
            password = user_data['password']
            if len(password) < 6:
                return {
                    'valid': False,
                    'message': '密码长度至少6个字符'
                }
        
        return {
            'valid': True,
            'message': '数据验证通过'
        }
    
    def _hash_password(self, password: str) -> str:
        """密码哈希"""
        return hashlib.sha256(password.encode()).hexdigest()

    def get_user_by_id(self, user_id: str) -> Dict[str, Any]:
        """根据ID获取用户详细信息"""
        try:
            # 优先使用全局缓存数据
            try:
                from cache.global_data_manager import global_data_manager
                if global_data_manager._data_loaded:
                    for user in global_data_manager._user_data:
                        # 支持多种ID匹配方式
                        if (user.get('user_id') == user_id or
                            user.get('id') == user_id or
                            str(user.get('id')) == str(user_id) or
                            str(user.get('user_id')) == str(user_id)):
                            return {'success': True, 'data': user}

                    # 如果没找到，记录调试信息
                    logger.warning(f"在缓存中未找到用户: {user_id}")
                    logger.debug(f"缓存中的用户ID列表: {[u.get('user_id', u.get('id')) for u in global_data_manager._user_data]}")
            except Exception as e:
                logger.warning(f"全局缓存获取用户详情失败: {e}")

            # 使用API模式
            if self.use_api:
                logger.info(f"使用API获取用户详情: {user_id}")
                response = self.api_client.get(f'/api/admin/v1/users/{user_id}')
                if response and response.get('success'):
                    return response
                else:
                    logger.error(f"API获取用户详情失败: {response}")
                    return {'success': False, 'message': 'API获取用户详情失败', 'data': None}

            # 使用本地数据库模式
            session = self.db_manager.get_session()
            user_dao = UserDAO(session)

            user = user_dao.get_by_id(user_id)
            if user:
                return {
                    'success': True,
                    'data': {
                        'id': user.id,
                        'user_id': user.user_id,
                        'username': user.username,
                        'email': user.email,
                        'display_name': user.display_name,
                        'phone': user.phone,
                        'avatar': user.avatar_url,
                        'is_active': user.is_active,
                        'is_admin': user.is_admin,
                        'last_login_at': user.last_login_at.isoformat() if user.last_login_at else None,
                        'created_at': user.created_at.isoformat() if user.created_at else None,
                        'updated_at': user.updated_at.isoformat() if user.updated_at else None
                    }
                }
            else:
                return {'success': False, 'message': '用户不存在', 'data': None}

        except Exception as e:
            logger.error(f"获取用户详情失败: {str(e)}")
            return {'success': False, 'message': f'获取用户详情失败: {str(e)}', 'data': None}

    def get_user_purchases(self, user_id: str) -> Dict[str, Any]:
        """获取用户购买记录"""
        try:
            # 优先使用全局缓存数据
            try:
                from cache.global_data_manager import global_data_manager
                if global_data_manager._data_loaded:
                    cached_data = global_data_manager.get_user_purchases(user_id)
                    if cached_data:
                        return cached_data
            except Exception as e:
                logger.warning(f"全局缓存获取用户购买记录失败: {e}")

            # 使用API模式
            if self.use_api:
                response = self.api_client.get(f'/api/admin/v1/users/{user_id}/purchases')
                if response and response.get('success'):
                    return response
                else:
                    return {'success': False, 'message': 'API获取购买记录失败', 'data': []}

            # 使用本地数据库模式（暂时返回空数据）
            return {'success': True, 'data': []}

        except Exception as e:
            logger.error(f"获取用户购买记录失败: {str(e)}")
            return {'success': False, 'message': f'获取用户购买记录失败: {str(e)}', 'data': []}

    def get_user_progress(self, user_id: str) -> Dict[str, Any]:
        """获取用户观看进度"""
        try:
            # 优先使用全局缓存数据
            try:
                from cache.global_data_manager import global_data_manager
                if global_data_manager._data_loaded:
                    cached_data = global_data_manager.get_user_progress(user_id)
                    if cached_data:
                        return cached_data
            except Exception as e:
                logger.warning(f"全局缓存获取用户观看进度失败: {e}")

            # 使用API模式
            if self.use_api:
                response = self.api_client.get(f'/api/admin/v1/users/{user_id}/progress')
                if response and response.get('success'):
                    return response
                else:
                    return {'success': False, 'message': 'API获取观看进度失败', 'data': []}

            # 使用本地数据库模式（暂时返回空数据）
            return {'success': True, 'data': []}

        except Exception as e:
            logger.error(f"获取用户观看进度失败: {str(e)}")
            return {'success': False, 'message': f'获取用户观看进度失败: {str(e)}', 'data': []}

    def get_user_favorites(self, user_id: str) -> Dict[str, Any]:
        """获取用户收藏"""
        try:
            # 优先使用全局缓存数据
            try:
                from cache.global_data_manager import global_data_manager
                if global_data_manager._data_loaded:
                    cached_data = global_data_manager.get_user_favorites(user_id)
                    if cached_data:
                        return cached_data
            except Exception as e:
                logger.warning(f"全局缓存获取用户收藏失败: {e}")

            # 使用API模式
            if self.use_api:
                response = self.api_client.get(f'/api/admin/v1/users/{user_id}/favorites')
                if response and response.get('success'):
                    return response
                else:
                    return {'success': False, 'message': 'API获取收藏失败', 'data': []}

            # 使用本地数据库模式（暂时返回空数据）
            return {'success': True, 'data': []}

        except Exception as e:
            logger.error(f"获取用户收藏失败: {str(e)}")
            return {'success': False, 'message': f'获取用户收藏失败: {str(e)}', 'data': []}

    def get_user_settings(self, user_id: str) -> Dict[str, Any]:
        """获取用户设置"""
        try:
            # 优先使用全局缓存数据
            try:
                from cache.global_data_manager import global_data_manager
                if global_data_manager._data_loaded:
                    cached_data = global_data_manager.get_user_settings(user_id)
                    if cached_data:
                        return cached_data
            except Exception as e:
                logger.warning(f"全局缓存获取用户设置失败: {e}")

            # 使用API模式
            if self.use_api:
                response = self.api_client.get(f'/api/admin/v1/users/{user_id}/settings')
                if response and response.get('success'):
                    return response
                else:
                    return {'success': False, 'message': 'API获取用户设置失败', 'data': []}

            # 使用本地数据库模式（暂时返回空数据）
            return {'success': True, 'data': []}

        except Exception as e:
            logger.error(f"获取用户设置失败: {str(e)}")
            return {'success': False, 'message': f'获取用户设置失败: {str(e)}', 'data': []}

    def get_user_cache(self, user_id: str) -> Dict[str, Any]:
        """获取用户缓存信息"""
        try:
            # 优先使用全局缓存数据
            try:
                from cache.global_data_manager import global_data_manager
                if global_data_manager._data_loaded:
                    cached_data = global_data_manager.get_user_cache(user_id)
                    if cached_data:
                        return cached_data
            except Exception as e:
                logger.warning(f"全局缓存获取用户缓存失败: {e}")

            # 使用API模式
            if self.use_api:
                response = self.api_client.get(f'/api/admin/v1/users/{user_id}/cache')
                if response and response.get('success'):
                    return response
                else:
                    return {'success': False, 'message': 'API获取缓存信息失败', 'data': []}

            # 使用本地数据库模式（暂时返回空数据）
            return {'success': True, 'data': []}

        except Exception as e:
            logger.error(f"获取用户缓存信息失败: {str(e)}")
            return {'success': False, 'message': f'获取用户缓存信息失败: {str(e)}', 'data': []}

    def update_user_info(self, user_id: str, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新用户信息 - 完整同步流程"""
        try:
            # 第1步：使用API更新服务器数据库
            if self.use_api:
                response = self.api_client.put(f'/api/admin/v1/users/{user_id}', user_data)
                if response and response.get('success'):
                    # 第2步：更新内存缓存（API模式下跳过本地数据库同步）
                    self.update_user_cache(user_id, user_data)

                    return {
                        'success': True,
                        'message': '用户信息更新成功',
                        'sync_status': {
                            'server_db': True,
                            'local_db': 'skipped_api_mode',  # API模式下跳过
                            'memory_cache': True
                        }
                    }
                else:
                    return {'success': False, 'message': 'API更新用户信息失败'}

            # 使用本地数据库模式
            return self.update_user_local_only(user_id, user_data)

        except Exception as e:
            logger.error(f"更新用户信息失败: {str(e)}")
            return {'success': False, 'message': f'更新用户信息失败: {str(e)}'}

    def reset_user_password(self, user_id: str) -> Dict[str, Any]:
        """重置用户密码"""
        try:
            # 使用API模式
            if self.use_api:
                response = self.api_client.post(f'/api/admin/v1/users/{user_id}/reset-password')
                if response and response.get('success'):
                    return response
                else:
                    return {'success': False, 'message': 'API重置密码失败'}

            # 使用本地数据库模式
            return {'success': False, 'message': '本地数据库重置密码功能待实现'}

        except Exception as e:
            logger.error(f"重置用户密码失败: {str(e)}")
            return {'success': False, 'message': f'重置用户密码失败: {str(e)}'}

    def toggle_user_status(self, user_id: str) -> Dict[str, Any]:
        """切换用户状态 - 完整同步流程"""
        try:
            # 使用API模式
            if self.use_api:
                response = self.api_client.post(f'/api/admin/v1/users/{user_id}/toggle-status')
                if response and response.get('success'):
                    new_status = response.get('new_status')

                    # 更新内存缓存（API模式下跳过本地数据库同步）
                    self.update_user_status_cache(user_id, new_status)

                    return {
                        'success': True,
                        'message': f'用户状态切换成功',
                        'new_status': new_status,
                        'sync_status': {
                            'server_db': True,
                            'local_db': 'skipped_api_mode',  # API模式下跳过
                            'memory_cache': True
                        }
                    }
                else:
                    return {'success': False, 'message': 'API切换用户状态失败'}

            # 使用本地数据库模式
            return self.toggle_user_status_local_only(user_id)

        except Exception as e:
            logger.error(f"切换用户状态失败: {str(e)}")
            return {'success': False, 'message': f'切换用户状态失败: {str(e)}'}

    def toggle_user_admin(self, user_id: str) -> Dict[str, Any]:
        """切换用户管理员权限"""
        try:
            # 使用API模式
            if self.use_api:
                response = self.api_client.post(f'/api/admin/v1/users/{user_id}/toggle-admin')
                if response and response.get('success'):
                    # 更新全局缓存中的用户权限
                    self.update_user_admin_cache(user_id, response.get('new_admin_status'))
                    return response
                else:
                    return {'success': False, 'message': 'API切换用户权限失败'}

            # 使用本地数据库模式
            return {'success': False, 'message': '本地数据库权限切换功能待实现'}

        except Exception as e:
            logger.error(f"切换用户权限失败: {str(e)}")
            return {'success': False, 'message': f'切换用户权限失败: {str(e)}'}

    def update_user_cache(self, user_id: str, user_data: Dict[str, Any]):
        """更新全局缓存中的用户信息"""
        try:
            from cache.global_data_manager import global_data_manager
            if global_data_manager._data_loaded:
                # 更新用户基本信息缓存
                for user in global_data_manager._user_data:
                    if (user.get('user_id') == user_id or
                        user.get('id') == user_id or
                        str(user.get('id')) == str(user_id) or
                        str(user.get('user_id')) == str(user_id)):
                        user.update(user_data)
                        break

                # 更新用户详情缓存
                if user_id in global_data_manager._user_details:
                    global_data_manager._user_details[user_id].update(user_data)

        except Exception as e:
            logger.warning(f"更新用户缓存失败: {e}")

    def update_user_status_cache(self, user_id: str, new_status: bool):
        """更新全局缓存中的用户状态"""
        try:
            from cache.global_data_manager import global_data_manager
            if global_data_manager._data_loaded:
                # 更新用户基本信息缓存
                for user in global_data_manager._user_data:
                    if (user.get('user_id') == user_id or
                        user.get('id') == user_id or
                        str(user.get('id')) == str(user_id) or
                        str(user.get('user_id')) == str(user_id)):
                        user['is_active'] = new_status
                        break

                # 更新用户详情缓存
                if user_id in global_data_manager._user_details:
                    global_data_manager._user_details[user_id]['is_active'] = new_status

        except Exception as e:
            logger.warning(f"更新用户状态缓存失败: {e}")

    def update_user_admin_cache(self, user_id: str, new_admin_status: bool):
        """更新全局缓存中的用户权限"""
        try:
            from cache.global_data_manager import global_data_manager
            if global_data_manager._data_loaded:
                # 更新用户基本信息缓存
                for user in global_data_manager._user_data:
                    if (user.get('user_id') == user_id or
                        user.get('id') == user_id or
                        str(user.get('id')) == str(user_id) or
                        str(user.get('user_id')) == str(user_id)):
                        user['is_admin'] = new_admin_status
                        break

                # 更新用户详情缓存
                if user_id in global_data_manager._user_details:
                    global_data_manager._user_details[user_id]['is_admin'] = new_admin_status

        except Exception as e:
            logger.warning(f"更新用户权限缓存失败: {e}")

    def sync_user_to_local_db(self, user_id: str, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """同步用户信息到本地数据库"""
        try:
            if not self.db_manager:
                return {'success': False, 'message': '本地数据库管理器未初始化'}

            # 构建更新SQL
            update_fields = []
            params = []

            if 'username' in user_data:
                update_fields.append("username = %s")
                params.append(user_data['username'])

            if 'email' in user_data:
                update_fields.append("email = %s")
                params.append(user_data['email'])

            if 'display_name' in user_data:
                update_fields.append("display_name = %s")
                params.append(user_data['display_name'])

            if 'phone' in user_data:
                update_fields.append("phone = %s")
                params.append(user_data['phone'])

            if 'is_active' in user_data:
                update_fields.append("is_active = %s")
                params.append(user_data['is_active'])

            if 'is_admin' in user_data:
                update_fields.append("is_admin = %s")
                params.append(user_data['is_admin'])

            if not update_fields:
                return {'success': True, 'message': '没有需要更新的字段'}

            # 添加更新时间
            update_fields.append("updated_at = NOW()")
            params.append(user_id)

            sql = f"""
                UPDATE users
                SET {', '.join(update_fields)}
                WHERE id = %s
            """

            # 执行更新 - 转换为元组
            affected_rows = self.db_manager.execute_update(sql, tuple(params))

            if affected_rows > 0:
                return {'success': True, 'message': f'本地数据库更新成功，影响 {affected_rows} 行'}
            else:
                return {'success': False, 'message': '本地数据库更新失败，没有影响任何行'}

        except Exception as e:
            logger.error(f"同步用户信息到本地数据库失败: {str(e)}")
            return {'success': False, 'message': f'本地数据库同步失败: {str(e)}'}

    def update_user_local_only(self, user_id: str, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """仅更新本地数据库和缓存（不使用API）"""
        try:
            # 第1步：更新本地数据库
            local_result = self.sync_user_to_local_db(user_id, user_data)

            if local_result.get('success'):
                # 第2步：更新内存缓存
                self.update_user_cache(user_id, user_data)

                return {
                    'success': True,
                    'message': '用户信息更新成功（仅本地）',
                    'sync_status': {
                        'server_db': False,
                        'local_db': True,
                        'memory_cache': True
                    }
                }
            else:
                return local_result

        except Exception as e:
            logger.error(f"本地更新用户信息失败: {str(e)}")
            return {'success': False, 'message': f'本地更新失败: {str(e)}'}

    def toggle_user_status_local_only(self, user_id: str) -> Dict[str, Any]:
        """仅在本地切换用户状态"""
        try:
            if not self.db_manager:
                return {'success': False, 'message': '本地数据库管理器未初始化'}

            # 获取当前状态
            current_user = self.db_manager.execute_query(
                "SELECT is_active FROM users WHERE id = %s", (user_id,)
            )

            if not current_user:
                return {'success': False, 'message': '用户不存在'}

            current_status = bool(current_user[0]['is_active'])
            new_status = not current_status

            # 更新状态
            local_result = self.sync_user_to_local_db(user_id, {'is_active': new_status})

            if local_result.get('success'):
                # 更新内存缓存
                self.update_user_status_cache(user_id, new_status)

                return {
                    'success': True,
                    'message': f'用户状态切换成功（仅本地）',
                    'new_status': new_status,
                    'sync_status': {
                        'server_db': False,
                        'local_db': True,
                        'memory_cache': True
                    }
                }
            else:
                return local_result

        except Exception as e:
            logger.error(f"本地切换用户状态失败: {str(e)}")
            return {'success': False, 'message': f'本地状态切换失败: {str(e)}'}

    def add_user_purchase(self, user_id: str, purchase_data: Dict[str, Any]) -> Dict[str, Any]:
        """添加用户购买记录"""
        try:
            # 使用API模式
            if self.use_api:
                response = self.api_client.post(f'/api/admin/v1/users/{user_id}/purchases', purchase_data)
                if response and response.get('success'):
                    # 更新全局缓存中的购买记录
                    self.update_purchase_cache(user_id, response.get('data'))

                    return {
                        'success': True,
                        'message': '购买记录添加成功',
                        'data': response.get('data')
                    }
                else:
                    return {'success': False, 'message': 'API添加购买记录失败'}

            # 使用本地数据库模式
            return self.add_user_purchase_local_only(user_id, purchase_data)

        except Exception as e:
            logger.error(f"添加用户购买记录失败: {str(e)}")
            return {'success': False, 'message': f'添加购买记录失败: {str(e)}'}

    def delete_user_purchase(self, user_id: str, purchase_id: str) -> Dict[str, Any]:
        """删除用户购买记录"""
        try:
            # 使用API模式
            if self.use_api:
                response = self.api_client.delete(f'/api/admin/v1/users/{user_id}/purchases/{purchase_id}')
                return response
            else:
                # 使用本地数据库
                if not self.db_manager:
                    return {'success': False, 'message': '本地数据库管理器未初始化'}

                result = self.db_manager.delete_user_purchase(user_id, purchase_id)
                return result

        except Exception as e:
            logger.error(f"删除用户购买记录失败: {str(e)}")
            return {'success': False, 'message': f'删除购买记录失败: {str(e)}'}

    def add_user_purchase_local_only(self, user_id: str, purchase_data: Dict[str, Any]) -> Dict[str, Any]:
        """仅在本地添加购买记录"""
        try:
            if not self.db_manager:
                return {'success': False, 'message': '本地数据库管理器未初始化'}

            # 插入购买记录到本地数据库
            insert_sql = """
                INSERT INTO user_purchases
                (user_id, item_type, item_id, purchase_price, status, purchase_date)
                VALUES (%s, %s, %s, %s, %s, NOW())
            """

            params = (
                user_id,
                purchase_data.get('type'),
                purchase_data.get('item_id'),
                purchase_data.get('amount'),
                purchase_data.get('status', 'active')
            )

            purchase_id = self.db_manager.execute_insert(insert_sql, params)

            if purchase_id:
                # 构建返回数据
                result_data = {
                    'id': purchase_id,
                    'user_id': user_id,
                    'item_type': purchase_data.get('type'),
                    'item_id': purchase_data.get('item_id'),
                    'item_name': purchase_data.get('item_name'),
                    'purchase_price': purchase_data.get('amount'),
                    'status': purchase_data.get('status', 'active')
                }

                # 更新缓存
                self.update_purchase_cache(user_id, result_data)

                return {
                    'success': True,
                    'message': '购买记录添加成功（仅本地）',
                    'data': result_data
                }
            else:
                return {'success': False, 'message': '本地数据库插入失败'}

        except Exception as e:
            logger.error(f"本地添加购买记录失败: {str(e)}")
            return {'success': False, 'message': f'本地添加失败: {str(e)}'}

    def update_purchase_cache(self, user_id: str, purchase_data: Dict[str, Any]):
        """更新购买记录缓存"""
        try:
            from cache.global_data_manager import global_data_manager
            if global_data_manager._data_loaded:
                # 如果用户购买记录缓存存在，添加新记录
                if user_id in global_data_manager._user_purchases:
                    current_purchases = global_data_manager._user_purchases[user_id]
                    if current_purchases.get('success') and 'data' in current_purchases:
                        current_purchases['data'].append(purchase_data)
                    else:
                        global_data_manager._user_purchases[user_id] = {
                            'success': True,
                            'data': [purchase_data]
                        }
                else:
                    global_data_manager._user_purchases[user_id] = {
                        'success': True,
                        'data': [purchase_data]
                    }

        except Exception as e:
            logger.warning(f"更新购买记录缓存失败: {e}")

    def remove_user_from_cache(self, user_id: str):
        """从全局缓存中移除用户数据"""
        try:
            from cache.global_data_manager import global_data_manager
            if global_data_manager._data_loaded:
                # 从用户基本信息缓存中移除
                global_data_manager._user_data = [
                    user for user in global_data_manager._user_data 
                    if not (user.get('user_id') == user_id or 
                           user.get('id') == user_id or
                           str(user.get('id')) == str(user_id) or
                           str(user.get('user_id')) == str(user_id))
                ]
                
                # 从用户详情缓存中移除
                if user_id in global_data_manager._user_details:
                    del global_data_manager._user_details[user_id]
                    
                # 从其他用户相关缓存中移除
                cache_keys = ['_user_purchases', '_user_progress', '_user_favorites', '_user_settings', '_user_cache']
                for cache_key in cache_keys:
                    if hasattr(global_data_manager, cache_key):
                        cache_dict = getattr(global_data_manager, cache_key)
                        if user_id in cache_dict:
                            del cache_dict[user_id]
                            
                logger.info(f"用户缓存已清理: {user_id}")
                
        except Exception as e:
            logger.warning(f"清理用户缓存失败: {e}")
