#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据模型定义
"""

from sqlalchemy import create_engine, Column, Integer, String, Text, DECIMAL, Boolean, TIMESTAMP, ForeignKey, Enum, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
from sqlalchemy.sql import func
from datetime import datetime
from typing import Optional, List

Base = declarative_base()

class User(Base):
    """用户模型"""
    __tablename__ = 'users'
    
    id = Column(Integer, primary_key=True, autoincrement=True, comment='自增主键')
    user_id = Column(String(50), nullable=False, unique=True, comment='用户UUID')
    username = Column(String(50), nullable=False, unique=True, comment='用户名')
    email = Column(String(100), nullable=False, unique=True, comment='邮箱')
    password_hash = Column(String(255), nullable=False, comment='密码哈希')
    display_name = Column(String(100), comment='显示名')
    phone = Column(String(20), comment='手机号')
    avatar_url = Column(String(255), comment='头像URL')
    is_active = Column(Boolean, default=True, comment='是否激活')
    is_admin = Column(Boolean, default=False, comment='是否管理员')
    last_login_at = Column(TIMESTAMP, comment='最后登录时间')
    created_at = Column(TIMESTAMP, default=func.current_timestamp(), comment='创建时间')
    updated_at = Column(TIMESTAMP, default=func.current_timestamp(), 
                       onupdate=func.current_timestamp(), comment='更新时间')
    
    # 关系
    orders = relationship("Order", back_populates="user", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<User(id={self.id}, user_id='{self.user_id}', username='{self.username}')>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'username': self.username,
            'email': self.email,
            'password_hash': self.password_hash,  # 🎯 修复：添加密码字段
            'display_name': self.display_name,
            'phone': self.phone,
            'avatar_url': self.avatar_url,
            'is_active': self.is_active,
            'is_admin': self.is_admin,
            'last_login_at': self.last_login_at.isoformat() if self.last_login_at else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class Series(Base):
    """系列模型"""
    __tablename__ = 'series'

    id = Column(String(50), primary_key=True, comment='系列ID（字符串）')
    title = Column(String(200), nullable=False, comment='标题')
    description = Column(Text, comment='描述')
    price = Column(DECIMAL(10, 2), default=0.00, comment='价格')
    is_free = Column(Boolean, default=False, comment='是否免费（独立于价格的明确标识）')
    is_published = Column(Boolean, default=False, comment='是否发布')
    created_at = Column(TIMESTAMP, default=func.current_timestamp(), comment='创建时间')
    updated_at = Column(TIMESTAMP, default=func.current_timestamp(),
                       onupdate=func.current_timestamp(), comment='更新时间')
    
    # 关系
    categories = relationship("Category", back_populates="series", cascade="all, delete-orphan")
    orders = relationship("Order", back_populates="series", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Series(id={self.id}, title='{self.title}', price={self.price})>"
    
    def to_dict(self):
        """转换为字典"""
        # 🎯 修复：安全地访问categories，避免Session问题
        try:
            # 检查categories是否已加载，避免触发lazy loading
            if hasattr(self, '_sa_instance_state') and self._sa_instance_state.session is None:
                # 对象已脱离Session，不访问关联属性
                category_count = 0
                total_videos = 0
            else:
                # 对象仍在Session中，可以安全访问
                category_count = len(self.categories) if self.categories else 0
                total_videos = sum(len(category.videos) for category in self.categories) if self.categories else 0
        except Exception:
            # 发生任何异常，使用默认值
            category_count = 0
            total_videos = 0

        return {
            'id': self.id,
            'title': self.title,
            'description': self.description,
            'price': float(self.price) if self.price else 0.0,  # 使用系列自身价格
            'is_free': self.is_free,  # 添加免费标识字段
            'is_published': self.is_published,
            'category_count': category_count,
            'video_count': total_videos,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class Category(Base):
    """分类模型"""
    __tablename__ = 'categories'

    id = Column(String(50), primary_key=True, comment='分类ID（字符串）')
    series_id = Column(String(50), ForeignKey('series.id'), nullable=False, comment='所属系列ID')
    title = Column(String(200), nullable=False, comment='分类标题')
    description = Column(Text, comment='分类描述')
    price = Column(DECIMAL(10, 2), default=0.00, comment='分类价格')
    order_index = Column(Integer, default=0, comment='排序序号')
    created_at = Column(TIMESTAMP, default=func.current_timestamp(), comment='创建时间')
    updated_at = Column(TIMESTAMP, default=func.current_timestamp(),
                       onupdate=func.current_timestamp(), comment='更新时间')

    # 关系
    series = relationship("Series", back_populates="categories")
    videos = relationship("Video", back_populates="category", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Category(id={self.id}, title='{self.title}', series_id={self.series_id}, price={self.price})>"

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'series_id': self.series_id,
            'series_title': self.series.title if self.series else None,
            'title': self.title,
            'description': self.description,
            'price': float(self.price) if self.price else 0.0,
            'order_index': self.order_index,
            'video_count': len(self.videos) if self.videos else 0,
            'total_duration': sum(video.duration or 0 for video in self.videos) if self.videos else 0,
            'total_duration_formatted': self.format_duration(sum(video.duration or 0 for video in self.videos) if self.videos else 0),
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

    def format_duration(self, seconds):
        """格式化时长显示"""
        if not seconds:
            return "00:00"

        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        seconds = seconds % 60

        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        else:
            return f"{minutes:02d}:{seconds:02d}"

class Video(Base):
    """视频模型"""
    __tablename__ = 'videos'
    
    id = Column(String(50), primary_key=True, comment='视频ID（字符串）')
    category_id = Column(String(50), ForeignKey('categories.id'), nullable=False, comment='所属分类ID')
    title = Column(String(200), nullable=False, comment='标题')
    description = Column(Text, comment='描述')
    video_url = Column(String(500), comment='视频地址')
    duration = Column(Integer, default=0, comment='时长(秒)')
    order_index = Column(Integer, default=0, comment='排序')
    created_at = Column(TIMESTAMP, default=func.current_timestamp(), comment='创建时间')
    updated_at = Column(TIMESTAMP, default=func.current_timestamp(),
                       onupdate=func.current_timestamp(), comment='更新时间')

    # 关系
    category = relationship("Category", back_populates="videos")
    
    def __repr__(self):
        return f"<Video(id={self.id}, title='{self.title}', duration={self.duration})>"
    
    def to_dict(self):
        """转换为字典"""
        # 安全地获取关联对象信息，避免Session关闭后的访问问题
        category_title = None
        category_price = 0.0
        series_id = None
        series_title = None
        
        try:
            if self.category:
                category_title = self.category.title
                category_price = float(self.category.price) if self.category.price else 0.0
                series_id = self.category.series_id
                
                # 获取系列标题
                if self.category.series:
                    series_title = self.category.series.title
        except Exception as e:
            # 如果Session已关闭或关联对象不可访问，使用默认值
            print(f"⚠️ Video.to_dict() 关联查询失败: {e}")
            category_title = "待确认"
            series_title = "待确认"
        
        return {
            'id': self.id,
            'category_id': self.category_id,
            'category_title': category_title,
            'category_price': category_price,
            'series_id': series_id,
            'series_title': series_title,
            'title': self.title,
            'description': self.description,
            'video_url': self.video_url,
            'duration': self.duration,
            'duration_formatted': self.format_duration(),
            'order_index': self.order_index,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    def format_duration(self):
        """格式化时长显示"""
        if not self.duration:
            return "00:00"
        
        hours = self.duration // 3600
        minutes = (self.duration % 3600) // 60
        seconds = self.duration % 60
        
        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        else:
            return f"{minutes:02d}:{seconds:02d}"

class Order(Base):
    """订单模型"""
    __tablename__ = 'orders'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=True, comment='用户ID')
    series_id = Column(Integer, ForeignKey('series.id'), nullable=True, comment='系列ID')
    amount = Column(DECIMAL(10, 2), nullable=False, comment='金额')
    status = Column(Enum('pending', 'completed', 'cancelled', name='order_status'), 
                   default='pending', comment='状态')
    created_at = Column(TIMESTAMP, default=func.current_timestamp(), comment='创建时间')
    updated_at = Column(TIMESTAMP, default=func.current_timestamp(), 
                       onupdate=func.current_timestamp(), comment='更新时间')
    
    # 关系
    user = relationship("User", back_populates="orders")
    series = relationship("Series", back_populates="orders")
    
    def __repr__(self):
        return f"<Order(id={self.id}, amount={self.amount}, status='{self.status}')>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'user_name': self.user.username if self.user else None,
            'user_email': self.user.email if self.user else None,
            'series_id': self.series_id,
            'series_title': self.series.title if self.series else None,
            'amount': float(self.amount) if self.amount else 0.0,
            'status': self.status,
            'status_display': self.get_status_display(),
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    def get_status_display(self):
        """获取状态显示文本"""
        status_map = {
            'pending': '待支付',
            'completed': '已完成',
            'cancelled': '已取消'
        }
        return status_map.get(self.status, self.status)

class UserPurchase(Base):
    """用户购买记录模型"""
    __tablename__ = 'user_purchases'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(String(50), nullable=False, comment='用户ID')
    item_type = Column(String(50), nullable=False, comment='购买类型')
    item_id = Column(String(50), nullable=False, comment='购买项目ID')
    purchase_price = Column(DECIMAL(10, 2), nullable=False, comment='购买价格')
    status = Column(String(20), default='active', comment='状态')
    purchase_date = Column(TIMESTAMP, default=func.current_timestamp(), comment='购买时间')
    created_at = Column(TIMESTAMP, default=func.current_timestamp(), comment='创建时间')
    updated_at = Column(TIMESTAMP, default=func.current_timestamp(),
                       onupdate=func.current_timestamp(), comment='更新时间')
    
    def __repr__(self):
        return f"<UserPurchase(id={self.id}, user_id='{self.user_id}', item_id='{self.item_id}')>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'item_type': self.item_type,
            'item_id': self.item_id,
            'purchase_price': float(self.purchase_price) if self.purchase_price else 0.0,
            'status': self.status,
            'purchase_date': self.purchase_date.isoformat() if self.purchase_date else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class CompensationOutbox(Base):
    """补偿表（Outbox）- 用于最终一致性保障"""
    __tablename__ = 'compensation_outbox'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    entity_type = Column(String(50), nullable=False, comment='实体类型（series/category/video）')
    entity_id = Column(String(50), nullable=False, comment='实体ID')
    operation_type = Column(String(20), nullable=False, comment='操作类型（create/update/delete）')
    payload = Column(Text, nullable=False, comment='操作数据（JSON格式）')
    target_system = Column(String(50), nullable=False, comment='目标系统（local_db/memory_cache）')
    retry_count = Column(Integer, default=0, comment='重试次数')
    max_retries = Column(Integer, default=5, comment='最大重试次数')
    next_retry_at = Column(TIMESTAMP, nullable=True, comment='下次重试时间')
    status = Column(Enum('pending', 'processing', 'completed', 'failed', name='compensation_status'), 
                   default='pending', comment='状态')
    error_message = Column(Text, comment='错误信息')
    created_at = Column(TIMESTAMP, default=func.current_timestamp(), comment='创建时间')
    updated_at = Column(TIMESTAMP, default=func.current_timestamp(),
                       onupdate=func.current_timestamp(), comment='更新时间')
    
    def __repr__(self):
        return f"<CompensationOutbox(id={self.id}, entity_type='{self.entity_type}', entity_id='{self.entity_id}', operation_type='{self.operation_type}', status='{self.status}')>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'entity_type': self.entity_type,
            'entity_id': self.entity_id,
            'operation_type': self.operation_type,
            'payload': self.payload,
            'target_system': self.target_system,
            'retry_count': self.retry_count,
            'max_retries': self.max_retries,
            'next_retry_at': self.next_retry_at.isoformat() if self.next_retry_at else None,
            'status': self.status,
            'error_message': self.error_message,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    def should_retry(self) -> bool:
        """判断是否应该重试"""
        if self.status != 'pending':
            return False
        if self.retry_count >= self.max_retries:
            return False
        if self.next_retry_at and self.next_retry_at > datetime.now():
            return False
        return True
    
    def calculate_next_retry(self) -> datetime:
        """计算下次重试时间（指数退避）"""
        import math
        from datetime import timedelta
        delay_seconds = min(300, 2 ** self.retry_count)  # 最大5分钟
        return datetime.now() + timedelta(seconds=delay_seconds)


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, connection_string: str):
        self.engine = create_engine(connection_string, echo=False)
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
    
    def create_tables(self):
        """创建所有表"""
        Base.metadata.create_all(bind=self.engine)
        # 执行数据迁移
        self._migrate_data()

    def _migrate_data(self):
        """执行数据迁移"""
        try:
            session = self.get_session()

            # 检查是否需要迁移videos表
            try:
                result = session.execute(text("SHOW COLUMNS FROM videos LIKE 'category_id'"))
                if not result.fetchone():
                    print("开始执行videos表迁移...")

                    # 添加category_id字段
                    session.execute(text("ALTER TABLE `videos` ADD COLUMN `category_id` int DEFAULT NULL COMMENT '所属分类ID'"))
                    session.execute(text("ALTER TABLE `videos` ADD KEY `idx_category_id` (`category_id`)"))

                    # 为现有系列创建默认分类
                    session.execute(text("""
                    INSERT INTO `categories` (`series_id`, `title`, `description`, `price`, `order_index`)
                    SELECT
                        `id` as `series_id`,
                        CONCAT(`title`, ' - 默认分类') as `title`,
                        '系统自动创建的默认分类' as `description`,
                        COALESCE(`price`, 0.00) as `price`,
                        1 as `order_index`
                    FROM `series`
                    WHERE NOT EXISTS (
                        SELECT 1 FROM `categories` WHERE `categories`.`series_id` = `series`.`id`
                    )
                    """))

                    # 将现有视频关联到默认分类
                    session.execute(text("""
                    UPDATE `videos` v
                    JOIN `categories` c ON c.series_id = v.series_id
                    SET v.category_id = c.id
                    WHERE v.category_id IS NULL
                    AND c.title LIKE '%默认分类%'
                    """))

                    session.commit()
                    print("✅ videos表迁移完成")
                else:
                    print("videos表已是最新版本")
            except Exception as e:
                print(f"videos表迁移过程中出现错误: {e}")
                session.rollback()

            # 检查是否需要迁移users表
            try:
                result = session.execute(text("SHOW COLUMNS FROM users LIKE 'display_name'"))
                if not result.fetchone():
                    print("开始执行users表迁移...")
                    
                    # 添加新字段
                    session.execute(text("ALTER TABLE `users` ADD COLUMN `display_name` VARCHAR(100) COMMENT '显示名'"))
                    session.execute(text("ALTER TABLE `users` ADD COLUMN `phone` VARCHAR(20) COMMENT '手机号'"))
                    session.execute(text("ALTER TABLE `users` ADD COLUMN `is_admin` BOOLEAN DEFAULT FALSE COMMENT '是否管理员'"))
                    
                    session.commit()
                    print("✅ users表迁移完成")
                else:
                    print("users表已是最新版本")
            except Exception as e:
                print(f"users表迁移过程中出现错误: {e}")
                session.rollback()

            session.close()
        except Exception as e:
            print(f"数据迁移失败: {e}")
    
    def get_session(self):
        """获取数据库会话"""
        return self.SessionLocal()
    
    def execute_query(self, query: str, params: tuple = None):
        """执行查询语句"""
        session = self.get_session()
        try:
            result = session.execute(text(query), params)
            return result.fetchall()
        finally:
            session.close()
    
    def execute_update(self, query: str, params: tuple = None) -> int:
        """执行更新语句"""
        session = self.get_session()
        try:
            result = session.execute(text(query), params)
            session.commit()
            return result.rowcount
        except Exception as e:
            session.rollback()
            raise e
        finally:
            session.close()
    
    def execute_insert(self, query: str, params: tuple = None):
        """执行插入语句并返回插入的ID"""
        session = self.get_session()
        try:
            result = session.execute(text(query), params)
            session.commit()
            return result.lastrowid
        except Exception as e:
            session.rollback()
            raise e
        finally:
            session.close()
    
    def close(self):
        """关闭数据库连接"""
        self.engine.dispose()
