"""
管理端用户API接口
提供用户的CRUD操作 - 使用MySQL数据库
"""

import uuid
from datetime import datetime
from fastapi import APIRouter, HTTPException, Header, Query
from typing import List, Optional, Dict, Any
from pydantic import BaseModel
from ..database.mysql_manager import mysql_manager

router = APIRouter()

# 数据模型
class UserCreate(BaseModel):
    id: str  # UUID架构：客户端必须提供ID
    username: str
    email: str
    password: str
    phone: Optional[str] = ""
    avatar_url: Optional[str] = ""
    is_active: Optional[bool] = True

class UserUpdate(BaseModel):
    username: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    avatar_url: Optional[str] = None
    is_active: Optional[bool] = None

@router.get("/users")
def get_users_admin(
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    search: Optional[str] = Query(None),
    is_active: Optional[bool] = Query(None)
):
    """获取用户列表（管理端）"""
    try:
        # 构建查询条件
        where_conditions = ["1=1"]
        params = []

        if search:
            where_conditions.append("(username LIKE %s OR email LIKE %s)")
            params.extend([f"%{search}%", f"%{search}%"])

        if is_active is not None:
            where_conditions.append("is_active = %s")
            params.append(is_active)

        where_clause = " AND ".join(where_conditions)

        # 获取总数
        count_sql = f"""
            SELECT COUNT(*) as total 
            FROM users 
            WHERE {where_clause}
        """
        total_result = mysql_manager.execute_query(count_sql, params)
        total = total_result[0]['total'] if total_result else 0

        # 获取数据
        offset = (page - 1) * page_size
        data_sql = f"""
            SELECT id, user_id, username, email, display_name, phone, avatar_url,
                   is_active, is_admin, last_login_at, created_at, updated_at
            FROM users
            WHERE {where_clause}
            ORDER BY created_at DESC
            LIMIT %s OFFSET %s
        """
        params.extend([page_size, offset])
        users = mysql_manager.execute_query(data_sql, params)

        return {
            "success": True,
            "data": users,
            "pagination": {
                "page": page,
                "page_size": page_size,
                "total": total,
                "pages": (total + page_size - 1) // page_size
            }
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取用户列表失败: {str(e)}")

@router.post("/users")
def create_user_admin(user: UserCreate):
    """创建用户（管理端）"""
    try:
        # 🎯 UUID架构：必须使用客户端提供的ID
        if not user.id:
            raise HTTPException(status_code=400, detail="🎯 UUID架构要求：必须提供ID")

        new_id = user.id  # 🎯 强制使用客户端提供的UUID

        # 检查用户名和邮箱是否已存在
        existing_user = mysql_manager.execute_query("""
            SELECT id FROM users WHERE username = %s OR email = %s
        """, (user.username, user.email))

        if existing_user:
            raise HTTPException(status_code=400, detail="用户名或邮箱已存在")

        # 执行插入SQL
        affected_rows = mysql_manager.execute_update("""
            INSERT INTO users (user_id, username, email, password_hash, display_name, phone, avatar_url, is_active, is_admin, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
        """, (
            new_id,
            user.username,
            user.email,
            user.password,  # 注意：这里应该是哈希后的密码
            user.username,  # 默认显示名为用户名
            user.phone,
            user.avatar_url,
            user.is_active,
            False  # 默认不是管理员
        ))

        if affected_rows > 0:
            # 获取创建的用户
            created_user = mysql_manager.execute_query("""
                SELECT id, user_id, username, email, display_name, phone, avatar_url, is_active, is_admin, created_at, updated_at
                FROM users WHERE user_id = %s
            """, (new_id,))

            return {
                "success": True,
                "message": "用户创建成功",
                "data": created_user[0] if created_user else None
            }
        else:
            raise HTTPException(status_code=500, detail="创建用户失败")

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建用户失败: {str(e)}")

@router.put("/users/{user_id}")
def update_user_admin(user_id: str, user_data: dict):
    """更新用户（管理端）"""
    try:
        # 检查用户是否存在
        check_sql = "SELECT user_id, username, email, display_name, phone FROM users WHERE user_id = %s"
        existing_user = mysql_manager.execute_query(check_sql, (user_id,))

        if not existing_user:
            raise HTTPException(status_code=404, detail="用户不存在")

        # 更新用户信息
        update_sql = """
            UPDATE users
            SET username = %s, email = %s, display_name = %s, phone = %s, updated_at = NOW()
            WHERE user_id = %s
        """

        affected_rows = mysql_manager.execute_update(update_sql, (
            user_data.get('username'),
            user_data.get('email'),
            user_data.get('display_name'),
            user_data.get('phone'),
            user_id
        ))

        if affected_rows == 0:
            raise HTTPException(status_code=500, detail="更新失败")

        # 获取更新后的用户信息
        updated_user = mysql_manager.execute_query(check_sql, (user_id,))
        user_info = updated_user[0] if updated_user else None

        return {
            "success": True,
            "message": f"用户 {user_id} 信息更新成功",
            "data": {
                "user_id": user_info["user_id"],
                "username": user_info["username"],
                "email": user_info["email"],
                "display_name": user_info["display_name"],
                "phone": user_info["phone"]
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新用户信息失败: {str(e)}")

@router.delete("/users/{user_id}")
def delete_user_admin(user_id: str):
    """删除用户（管理端）"""
    try:
        # 检查用户是否存在
        existing_user = mysql_manager.execute_query("""
            SELECT * FROM users WHERE user_id = %s
        """, (user_id,))

        if not existing_user:
            raise HTTPException(status_code=404, detail="用户不存在")

        # 删除用户
        affected_rows = mysql_manager.execute_update("""
            DELETE FROM users WHERE user_id = %s
        """, (user_id,))

        if affected_rows > 0:
            return {
                "success": True,
                "message": "用户删除成功"
            }
        else:
            raise HTTPException(status_code=500, detail="删除用户失败")

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除用户失败: {str(e)}")

@router.get("/users/{user_id}")
def get_user_admin(user_id: str):
    """获取单个用户详情（管理端）"""
    try:
        user = mysql_manager.execute_query("""
            SELECT id, user_id, username, email, display_name, avatar_url, phone, password_hash, is_active, is_admin, last_login_at, created_at, updated_at
            FROM users WHERE user_id = %s
        """, (user_id,))

        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")

        return {
            "success": True,
            "data": user[0]
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取用户详情失败: {str(e)}")

@router.get("/users/{user_id}/purchases")
def get_user_purchases_admin(user_id: str):
    """获取用户购买记录（管理端）"""
    try:
        # 使用mysql_manager，它工作正常
        result = mysql_manager.get_user_purchases(user_id)

        return {
            "success": result.get('success', True),
            "data": result.get('data', []),
            "message": f"获取用户 {user_id} 购买记录成功"
        }

        # 转换为管理端需要的格式
        purchase_list = []

        # 处理系列购买记录
        for series_purchase in purchases.get('series', []):
            purchase_list.append({
                'id': series_purchase['id'],
                'type': 'series',
                'item_name': f"系列 {series_purchase['id']}",  # 后续可以关联实际名称
                'amount': series_purchase['amount'],
                'purchase_date': series_purchase['purchaseDate'],
                'payment_method': series_purchase.get('paymentMethod', ''),
                'transaction_id': series_purchase.get('transactionId', ''),
                'status': 'completed'
            })

        # 处理分类购买记录
        for category_purchase in purchases.get('categories', []):
            purchase_list.append({
                'id': category_purchase['id'],
                'type': 'category',
                'item_name': f"分类 {category_purchase['id']}",  # 后续可以关联实际名称
                'amount': category_purchase['amount'],
                'purchase_date': category_purchase['purchaseDate'],
                'payment_method': category_purchase.get('paymentMethod', ''),
                'transaction_id': category_purchase.get('transactionId', ''),
                'status': 'completed'
            })

        return {
            "success": True,
            "data": purchase_list,
            "message": f"获取用户 {user_id} 购买记录成功"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取用户购买记录失败: {str(e)}")

@router.get("/users/{user_id}/progress")
def get_user_progress_admin(user_id: str):
    """获取用户观看进度（管理端）- 显示所有可访问视频"""
    try:
        from src.database.mysql_manager import mysql_manager

        # 获取用户可访问的所有视频（已购买分类的视频 + 免费系列的视频）
        accessible_videos_sql = """
            SELECT DISTINCT v.id, v.title, v.category_id, v.series_id, 
                   c.title as category_title, s.title as series_title,
                   v.duration, v.order_index
            FROM videos v
            INNER JOIN categories c ON v.category_id = c.id
            INNER JOIN series s ON v.series_id = s.id
            WHERE v.status = 'published' AND (
                -- 用户已购买的分类下的视频
                v.category_id IN (
                    SELECT item_id FROM user_purchases 
                    WHERE user_id = %s AND item_type = 'category' 
                    AND status IN ('active', 'completed')
                )
                OR
                -- 免费系列下的所有视频
                v.series_id IN (
                    SELECT id FROM series 
                    WHERE (price = 0 OR is_free = 1) AND is_published = 1
                )
            )
            ORDER BY s.order_index, c.order_index, v.order_index
        """
        
        videos = mysql_manager.execute_query(accessible_videos_sql, (user_id,))
        
        # 获取这些视频的观看进度
        video_ids = [video['id'] for video in videos]
        progress_data = {}
        
        if video_ids:
            # 使用 IN 查询优化性能
            placeholders = ','.join(['%s'] * len(video_ids))
            progress_sql = f"""
                SELECT video_id, position as watch_position, watch_count, 
                       CASE WHEN progress_percentage >= 95 THEN 1 ELSE 0 END as is_completed, 
                       last_watched_at as last_watch_date
                FROM user_progress
                WHERE user_id = %s AND video_id IN ({placeholders})
            """
            
            progress_results = mysql_manager.execute_query(progress_sql, (user_id,) + tuple(video_ids))
            for progress in progress_results:
                progress_data[progress['video_id']] = {
                    'watch_position': progress['watch_position'],
                    'watch_count': progress['watch_count'],
                    'is_completed': progress['is_completed'],
                    'last_watch_date': progress['last_watch_date']
                }
            
        # 组合数据
        formatted_progress = []
        for video in videos:
            video_id = video['id']
            duration = video['duration'] or 300  # 默认5分钟
            
            # 获取进度数据，如果没有则使用默认值
            progress = progress_data.get(video_id, {})
            watch_position = progress.get('watch_position', 0)
            
            formatted_progress.append({
                'video_id': video_id,
                'video_title': video['title'] or f"视频 {video_id}",
                'category_title': video['category_title'],
                'series_title': video['series_title'],
                'progress_seconds': watch_position,
                'total_duration': duration,
                'progress_percentage': round((watch_position / duration * 100) if duration > 0 else 0, 2),
                'watch_count': progress.get('watch_count', 0),
                'completed': bool(progress.get('is_completed', False)),
                'last_watched': progress.get('last_watch_date').isoformat() if progress.get('last_watch_date') else None,
                'has_progress': video_id in progress_data  # 标记是否有观看记录
            })

        return {
            "success": True,
            "data": formatted_progress,
            "message": f"获取用户 {user_id} 可访问视频的观看进度成功"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取用户观看进度失败: {str(e)}")

@router.get("/users/{user_id}/favorites")
def get_user_favorites_admin(user_id: str):
    """获取用户收藏（管理端）- 显示所有可访问视频的收藏状态"""
    try:
        from src.database.mysql_manager import mysql_manager

        # 获取用户可访问的所有视频（复用进度API的逻辑）
        accessible_videos_sql = """
            SELECT DISTINCT v.id, v.title, v.category_id, v.series_id, 
                   c.title as category_title, s.title as series_title,
                   v.thumbnail_url, v.order_index
            FROM videos v
            INNER JOIN categories c ON v.category_id = c.id
            INNER JOIN series s ON v.series_id = s.id
            WHERE v.status = 'published' AND (
                -- 用户已购买的分类下的视频
                v.category_id IN (
                    SELECT item_id FROM user_purchases 
                    WHERE user_id = %s AND item_type = 'category' 
                    AND status IN ('active', 'completed')
                )
                OR
                -- 免费系列下的所有视频
                v.series_id IN (
                    SELECT id FROM series 
                    WHERE (price = 0 OR is_free = 1) AND is_published = 1
                )
            )
            ORDER BY s.order_index, c.order_index, v.order_index
        """
        
        videos = mysql_manager.execute_query(accessible_videos_sql, (user_id,))
        
        # 获取用户的收藏记录
        favorites_sql = """
            SELECT item_id, favorited_at 
            FROM user_favorites 
            WHERE user_id = %s AND item_type = 'video'
        """
        
        favorites_results = mysql_manager.execute_query(favorites_sql, (user_id,))
        favorites_data = {row['item_id']: row['favorited_at'] for row in favorites_results}
        
        # 组合数据
        formatted_favorites = []
        for video in videos:
            video_id = video['id']
            is_favorited = video_id in favorites_data
            
            formatted_favorites.append({
                'video_id': video_id,
                'video_title': video['title'] or f"视频 {video_id}",
                'category_title': video['category_title'],
                'series_title': video['series_title'],
                'thumbnail_url': video['thumbnail_url'],
                'is_favorited': is_favorited,
                'favorited_at': favorites_data.get(video_id).isoformat() if is_favorited and favorites_data.get(video_id) else None,
                'created_at': favorites_data.get(video_id).isoformat() if is_favorited and favorites_data.get(video_id) else None,
                'updated_at': favorites_data.get(video_id).isoformat() if is_favorited and favorites_data.get(video_id) else None,
                'type': 'video',  # 兼容原有格式
                'item_type': 'video',
                'item_id': video_id
            })

        return {
            "success": True,
            "data": formatted_favorites,
            "message": f"获取用户 {user_id} 可访问视频的收藏状态成功"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取用户收藏失败: {str(e)}")

@router.get("/users/{user_id}/cache")
def get_user_cache_admin(user_id: str):
    """获取用户缓存信息（管理端）- 显示所有可访问视频的缓存状态"""
    try:
        from src.database.mysql_manager import mysql_manager

        # 获取用户可访问的所有视频（复用进度API的逻辑）
        accessible_videos_sql = """
            SELECT DISTINCT v.id, v.title, v.category_id, v.series_id, 
                   c.title as category_title, s.title as series_title,
                   v.video_url, v.file_size, v.order_index
            FROM videos v
            INNER JOIN categories c ON v.category_id = c.id
            INNER JOIN series s ON v.series_id = s.id
            WHERE v.status = 'published' AND (
                -- 用户已购买的分类下的视频
                v.category_id IN (
                    SELECT item_id FROM user_purchases 
                    WHERE user_id = %s AND item_type = 'category' 
                    AND status IN ('active', 'completed')
                )
                OR
                -- 免费系列下的所有视频
                v.series_id IN (
                    SELECT id FROM series 
                    WHERE (price = 0 OR is_free = 1) AND is_published = 1
                )
            )
            ORDER BY s.order_index, c.order_index, v.order_index
        """
        
        videos = mysql_manager.execute_query(accessible_videos_sql, (user_id,))
        
        # 获取用户的缓存记录
        cache_sql = """
            SELECT video_id, is_cached, local_path, file_size, cached_at
            FROM user_cache
            WHERE user_id = %s
        """
        
        cache_results = mysql_manager.execute_query(cache_sql, (user_id,))
        cache_data = {}
        for cache in cache_results:
            cache_data[cache['video_id']] = {
                'is_cached': cache['is_cached'],
                'local_path': cache['local_path'],
                'cached_file_size': cache['file_size'],
                'cached_at': cache['cached_at']
            }
        
        # 组合数据
        formatted_cache = []
        for video in videos:
            video_id = video['id']
            video_file_size = video['file_size'] or 0  # 视频原始文件大小
            
            # 获取缓存数据，如果没有则使用默认值
            cache_info = cache_data.get(video_id, {})
            is_cached = cache_info.get('is_cached', False)
            
            formatted_cache.append({
                'video_id': video_id,
                'video_title': video['title'] or f"视频 {video_id}",
                'category_title': video['category_title'],
                'series_title': video['series_title'],
                'video_url': video['video_url'],
                'cache_status': is_cached,  # 兼容原有字段名
                'is_cached': is_cached,
                'local_path': cache_info.get('local_path', ''),
                'file_size': cache_info.get('cached_file_size', video_file_size),
                'file_size_mb': round((cache_info.get('cached_file_size', video_file_size) or 0) / 1024 / 1024, 2),
                'cached_at': cache_info.get('cached_at').isoformat() if cache_info.get('cached_at') else None,
                'has_cache_record': video_id in cache_data  # 标记是否有缓存记录
            })

        return {
            "success": True,
            "data": formatted_cache,
            "message": f"获取用户 {user_id} 可访问视频的缓存状态成功"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取用户缓存信息失败: {str(e)}")



@router.post("/users/{user_id}/reset-password")
def reset_user_password(user_id: str):
    """重置用户密码（管理端）- 🎯 修复：固定密码ultrathink，使用mysql_manager"""
    try:
        # 🎯 修复：使用固定密码 "ultrathink"
        new_password = "ultrathink"

        # 🎯 修复：使用mysql_manager以保持与其他API的一致性
        affected_rows = mysql_manager.execute_update("""
            UPDATE users SET password_hash = %s WHERE user_id = %s
        """, (new_password, user_id))

        if affected_rows == 0:
            raise HTTPException(status_code=404, detail="用户不存在")

        return {
            "success": True,
            "message": f"用户 {user_id} 密码重置成功",
            "new_password": new_password  # 管理端显示明文密码
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"重置用户密码失败: {str(e)}")

@router.post("/users/{user_id}/toggle-status")
def toggle_user_status(user_id: str):
    """切换用户状态（管理端）"""
    try:
        import pymysql

        # 创建数据库连接
        connection = pymysql.connect(
            host='localhost',
            user='shuimu_server',
            password='dyj217',
            database='shuimu_course_server',
            charset='utf8mb4'
        )

        with connection.cursor() as cursor:
            # 获取当前状态
            select_sql = "SELECT is_active FROM users WHERE user_id = %s"
            cursor.execute(select_sql, (user_id,))
            result = cursor.fetchone()

            if not result:
                raise HTTPException(status_code=404, detail="用户不存在")

            current_status = bool(result[0])
            new_status = not current_status

            # 更新状态
            update_sql = "UPDATE users SET is_active = %s WHERE user_id = %s"
            cursor.execute(update_sql, (new_status, user_id))
            connection.commit()

        connection.close()

        return {
            "success": True,
            "message": f"用户 {user_id} 状态切换成功",
            "new_status": new_status
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"切换用户状态失败: {str(e)}")

@router.post("/users/{user_id}/toggle-admin")
def toggle_user_admin(user_id: str):
    """切换用户管理员权限（管理端）"""
    try:
        import pymysql

        # 创建数据库连接
        connection = pymysql.connect(
            host='localhost',
            user='shuimu_server',
            password='dyj217',
            database='shuimu_course_server',
            charset='utf8mb4'
        )

        with connection.cursor() as cursor:
            # 获取当前权限
            select_sql = "SELECT is_admin FROM users WHERE user_id = %s"
            cursor.execute(select_sql, (user_id,))
            result = cursor.fetchone()

            if not result:
                raise HTTPException(status_code=404, detail="用户不存在")

            current_admin = bool(result[0])
            new_admin = not current_admin

            # 更新权限
            update_sql = "UPDATE users SET is_admin = %s WHERE user_id = %s"
            cursor.execute(update_sql, (new_admin, user_id))
            connection.commit()

        connection.close()

        return {
            "success": True,
            "message": f"用户 {user_id} 权限切换成功",
            "new_admin_status": new_admin
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"切换用户权限失败: {str(e)}")

@router.post("/users/{user_id}/purchases")
def add_user_purchase(
    user_id: str,
    purchase_data: dict,
    current_user_id: str = Header("admin", alias="X-User-Id"),
    is_admin: str = Header("true", alias="X-Is-Admin")
):
    """添加用户购买记录（管理端）"""
    try:
        # 验证管理员权限
        if is_admin != "true":
            raise HTTPException(status_code=403, detail="需要管理员权限")

        import pymysql
        from datetime import datetime

        # 创建数据库连接
        connection = pymysql.connect(
            host='localhost',
            user='shuimu_server',
            password='dyj217',
            database='shuimu_course_server',
            charset='utf8mb4'
        )

        with connection.cursor() as cursor:
            # 检查用户是否存在
            check_user_sql = "SELECT user_id FROM users WHERE user_id = %s"
            cursor.execute(check_user_sql, (user_id,))
            if not cursor.fetchone():
                connection.close()
                raise HTTPException(status_code=404, detail="用户不存在")

            purchase_type = purchase_data.get('type')
            item_id = purchase_data.get('item_id')
            amount = purchase_data.get('amount')

            if purchase_type == 'series':
                # 购买系列：展开为该系列下的所有分类
                # 1. 获取系列信息
                series_sql = "SELECT title FROM series WHERE id = %s"
                cursor.execute(series_sql, (item_id,))
                series_result = cursor.fetchone()
                if not series_result:
                    connection.close()
                    raise HTTPException(status_code=404, detail="系列不存在")

                series_name = series_result[0]

                # 2. 获取该系列下的所有分类
                categories_sql = "SELECT id, title, price FROM categories WHERE series_id = %s"
                cursor.execute(categories_sql, (item_id,))
                categories = cursor.fetchall()

                if not categories:
                    connection.close()
                    raise HTTPException(status_code=404, detail="该系列下没有分类")

                # 3. 为每个分类创建购买记录
                insert_sql = """
                    INSERT INTO user_purchases
                    (user_id, item_type, item_id, series_name, purchase_price, status)
                    VALUES (%s, %s, %s, %s, %s, %s)
                """

                purchase_ids = []
                for category in categories:
                    category_id, category_title, category_price = category
                    cursor.execute(insert_sql, (
                        user_id,
                        'category',  # 统一存储为分类类型
                        category_id,
                        series_name,  # 存储所属系列名称
                        category_price or amount,  # 使用分类价格，如果没有则使用系列价格
                        'active'
                    ))
                    purchase_ids.append(cursor.lastrowid)

                connection.commit()

                return {
                    "success": True,
                    "message": f"系列购买成功，已添加{len(categories)}个分类的购买记录",
                    "data": {
                        "series_name": series_name,
                        "categories_count": len(categories),
                        "purchase_ids": purchase_ids
                    }
                }

            else:
                # 购买单个分类
                # 1. 获取分类信息和所属系列
                category_sql = """
                    SELECT c.title, s.title as series_title
                    FROM categories c
                    LEFT JOIN series s ON c.series_id = s.id
                    WHERE c.id = %s
                """
                cursor.execute(category_sql, (item_id,))
                category_result = cursor.fetchone()
                if not category_result:
                    connection.close()
                    raise HTTPException(status_code=404, detail="分类不存在")

                category_title, series_title = category_result

                # 2. 插入单个分类购买记录
                insert_sql = """
                    INSERT INTO user_purchases
                    (user_id, item_type, item_id, series_name, purchase_price, status)
                    VALUES (%s, %s, %s, %s, %s, %s)
                """

                cursor.execute(insert_sql, (
                    user_id,
                    'category',  # 统一存储为分类类型
                    item_id,
                    series_title or '未知系列',  # 存储所属系列名称
                    amount,
                    'active'
                ))

                purchase_id = cursor.lastrowid
                connection.commit()

                return {
                    "success": True,
                    "message": f"分类购买成功",
                    "data": {
                        "id": purchase_id,
                        "category_name": category_title,
                        "series_name": series_title,
                        "amount": amount
                    }
                }

        connection.close()

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"添加购买记录失败: {str(e)}")

@router.delete("/users/{user_id}/purchases/{purchase_id}")
def delete_user_purchase_admin(user_id: str, purchase_id: str):
    """删除用户购买记录（管理端）"""
    try:
        import pymysql

        # 连接数据库
        connection = pymysql.connect(
            host='localhost',
            user='shuimu_server',
            password='dyj217',
            database='shuimu_course_server',
            charset='utf8mb4'
        )

        with connection.cursor() as cursor:
            # 检查购买记录是否存在
            check_sql = "SELECT id, item_id, series_name FROM user_purchases WHERE id = %s AND user_id = %s"
            cursor.execute(check_sql, (purchase_id, user_id))
            purchase_record = cursor.fetchone()

            if not purchase_record:
                connection.close()
                raise HTTPException(status_code=404, detail="购买记录不存在")

            # 删除购买记录
            delete_sql = "DELETE FROM user_purchases WHERE id = %s AND user_id = %s"
            cursor.execute(delete_sql, (purchase_id, user_id))

            if cursor.rowcount == 0:
                connection.close()
                raise HTTPException(status_code=404, detail="删除失败，记录不存在")

            connection.commit()

        connection.close()

        return {
            "success": True,
            "message": "购买记录删除成功"
        }

    except HTTPException:
        raise
    except Exception as e:
        print(f"删除购买记录异常: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"删除失败: {str(e)}")

@router.get("/users/{user_id}/settings")
def get_user_settings_admin(user_id: str):
    """获取用户设置（管理端）"""
    try:
        from ..utils.user_data_mysql import get_user_settings

        settings = get_user_settings(user_id)

        # 转换为管理端需要的格式
        formatted_settings = []
        for key, value in settings.items():
            formatted_settings.append({
                'key': key,
                'value': str(value),
                'type': type(value).__name__
            })

        return {
            "success": True,
            "data": formatted_settings,
            "message": f"获取用户 {user_id} 设置成功"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取用户设置失败: {str(e)}")
