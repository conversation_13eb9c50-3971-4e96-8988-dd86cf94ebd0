#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Users CRUD API - RESTful用户管理端点
"""

from fastapi import APIRouter, HTTPException, Query, Header, Depends
from typing import List, Optional, Dict, Any
from pydantic import BaseModel
import uuid
from datetime import datetime
import hashlib

router = APIRouter()

# 数据模型
class UserCreateRequest(BaseModel):
    username: str
    email: str
    password: str
    display_name: Optional[str] = None
    phone: Optional[str] = None
    is_admin: Optional[bool] = False

class UserUpdateRequest(BaseModel):
    username: Optional[str] = None
    email: Optional[str] = None
    display_name: Optional[str] = None
    phone: Optional[str] = None
    avatar_url: Optional[str] = None
    is_active: Optional[bool] = None
    is_admin: Optional[bool] = None

class UserProfileUpdateRequest(BaseModel):
    display_name: Optional[str] = None
    phone: Optional[str] = None
    avatar_url: Optional[str] = None

class PasswordChangeRequest(BaseModel):
    old_password: str
    new_password: str

# 权限验证
def get_current_user_id(x_user_id: Optional[str] = Header("admin_001")):
    """获取当前用户ID"""
    if not x_user_id:
        raise HTTPException(status_code=401, detail="用户认证必需")
    return x_user_id

def get_admin_status(x_is_admin: Optional[str] = Header(None)):
    """获取管理员状态"""
    return x_is_admin == "true"

def verify_admin_permission(is_admin: bool):
    """验证管理员权限"""
    if not is_admin:
        raise HTTPException(status_code=403, detail="需要管理员权限")

def verify_user_access_permission(current_user_id: str, target_user_id: str, is_admin: bool):
    """验证用户访问权限"""
    if is_admin:
        return True  # 管理员可以访问任何用户
    if current_user_id == target_user_id:
        return True  # 用户可以访问自己的数据
    raise HTTPException(status_code=403, detail="权限不足：无法访问其他用户数据")

def hash_password(password: str) -> str:
    """密码哈希"""
    return hashlib.sha256(password.encode()).hexdigest()

# API端点
@router.get("/users")
def get_users_list(
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=1000),
    search: Optional[str] = Query(None),
    is_active: Optional[bool] = Query(None),
    is_admin: Optional[bool] = Query(None),
    current_user_id: str = Depends(get_current_user_id),
    is_current_admin: bool = Depends(get_admin_status)
):
    """获取用户列表（管理端）"""
    try:
        # 验证管理员权限
        verify_admin_permission(is_current_admin)
        
        from ..database.mysql_manager import mysql_manager
        
        # 构建查询条件
        where_conditions = ["1=1"]
        params = []
        
        if search:
            where_conditions.append("(username LIKE %s OR email LIKE %s OR display_name LIKE %s)")
            params.extend([f"%{search}%", f"%{search}%", f"%{search}%"])
        
        if is_active is not None:
            where_conditions.append("is_active = %s")
            params.append(is_active)
            
        if is_admin is not None:
            where_conditions.append("is_admin = %s")
            params.append(is_admin)
        
        where_clause = " AND ".join(where_conditions)
        
        # 获取总数
        count_query = f"SELECT COUNT(*) as total FROM users WHERE {where_clause}"
        total_result = mysql_manager.execute_query(count_query, tuple(params))
        total = total_result[0]['total'] if total_result else 0
        
        # 获取分页数据
        offset = (page - 1) * page_size
        data_query = f"""
            SELECT user_id, username, email, display_name, avatar_url, phone, 
                   is_active, is_admin, last_login_at, created_at, updated_at
            FROM users 
            WHERE {where_clause}
            ORDER BY created_at DESC
            LIMIT %s OFFSET %s
        """
        params.extend([page_size, offset])
        users_data = mysql_manager.execute_query(data_query, tuple(params))
        
        # 转换数据格式
        for user in users_data:
            user['id'] = user['user_id']  # 统一使用user_id作为id
            if user['last_login_at']:
                user['last_login_at'] = user['last_login_at'].isoformat()
            if user['created_at']:
                user['created_at'] = user['created_at'].isoformat()
            if user['updated_at']:
                user['updated_at'] = user['updated_at'].isoformat()
        
        return {
            'success': True,
            'data': users_data,
            'pagination': {
                'page': page,
                'page_size': page_size,
                'total': total,
                'pages': (total + page_size - 1) // page_size
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取用户列表失败: {str(e)}")

@router.get("/users/{user_id}")
def get_user_by_id(
    user_id: str,
    current_user_id: str = Depends(get_current_user_id),
    is_admin: bool = Depends(get_admin_status)
):
    """根据ID获取用户信息"""
    try:
        # 验证访问权限
        verify_user_access_permission(current_user_id, user_id, is_admin)
        
        from ..database.mysql_manager import mysql_manager
        
        user = mysql_manager.execute_query("""
            SELECT user_id, username, email, display_name, avatar_url, phone, 
                   is_active, is_admin, last_login_at, created_at, updated_at
            FROM users WHERE user_id = %s
        """, (user_id,))
        
        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")
        
        user_data = user[0]
        user_data['id'] = user_data['user_id']
        
        # 转换时间格式
        for field in ['last_login_at', 'created_at', 'updated_at']:
            if user_data[field]:
                user_data[field] = user_data[field].isoformat()
        
        return {
            'success': True,
            'data': user_data
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取用户信息失败: {str(e)}")

@router.post("/admin/users")
def create_user(
    data: UserCreateRequest,
    current_user_id: str = Depends(get_current_user_id),
    is_admin: bool = Depends(get_admin_status)
):
    """创建用户（管理端）"""
    try:
        # 验证管理员权限
        verify_admin_permission(is_admin)
        
        from ..database.mysql_manager import mysql_manager
        
        # 检查用户名和邮箱是否已存在
        existing_user = mysql_manager.execute_query("""
            SELECT id FROM users WHERE username = %s OR email = %s
        """, (data.username, data.email))
        
        if existing_user:
            raise HTTPException(status_code=400, detail="用户名或邮箱已存在")
        
        # 生成新用户ID
        new_user_id = str(uuid.uuid4())
        
        # 🎯 修复：保存明文密码，不进行哈希处理
        password_hash = data.password  # 直接使用明文密码
        
        # 插入用户数据
        affected_rows = mysql_manager.execute_update("""
            INSERT INTO users (user_id, username, email, display_name, phone, password_hash, is_admin, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
        """, (
            new_user_id,
            data.username,
            data.email,
            data.display_name or data.username,
            data.phone,
            password_hash,
            data.is_admin
        ))
        
        if affected_rows > 0:
            # 验证用户是否创建成功
            created_user = mysql_manager.execute_query("""
                SELECT user_id, username, email, display_name, phone, is_active, is_admin, created_at
                FROM users WHERE user_id = %s
            """, (new_user_id,))
            
            if not created_user:
                raise HTTPException(status_code=500, detail="用户创建失败：数据未正确保存")
            
            user_data = created_user[0]
            user_data['id'] = user_data['user_id']
            user_data['created_at'] = user_data['created_at'].isoformat()
            
            print(f"✅ 用户创建成功: {data.username} (ID: {new_user_id})")
            
            return {
                "success": True,
                "message": "用户创建成功",
                "data": user_data
            }
        else:
            raise HTTPException(status_code=500, detail="创建用户失败：数据库操作未影响任何行")
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建用户失败: {str(e)}")

@router.put("/admin/users/{user_id}")
def update_user(
    user_id: str,
    data: UserUpdateRequest,
    current_user_id: str = Depends(get_current_user_id),
    is_admin: bool = Depends(get_admin_status)
):
    """更新用户信息（管理端）"""
    try:
        # 验证管理员权限
        verify_admin_permission(is_admin)

        from ..database.mysql_manager import mysql_manager

        # 检查用户是否存在
        existing_user = mysql_manager.execute_query("""
            SELECT user_id FROM users WHERE user_id = %s
        """, (user_id,))

        if not existing_user:
            raise HTTPException(status_code=404, detail="用户不存在")

        # 构建更新字段
        update_fields = []
        params = []

        if data.username is not None:
            # 检查用户名是否已被其他用户使用
            username_check = mysql_manager.execute_query("""
                SELECT user_id FROM users WHERE username = %s AND user_id != %s
            """, (data.username, user_id))
            if username_check:
                raise HTTPException(status_code=400, detail="用户名已被使用")
            update_fields.append("username = %s")
            params.append(data.username)

        if data.email is not None:
            # 检查邮箱是否已被其他用户使用
            email_check = mysql_manager.execute_query("""
                SELECT user_id FROM users WHERE email = %s AND user_id != %s
            """, (data.email, user_id))
            if email_check:
                raise HTTPException(status_code=400, detail="邮箱已被使用")
            update_fields.append("email = %s")
            params.append(data.email)

        if data.display_name is not None:
            update_fields.append("display_name = %s")
            params.append(data.display_name)

        if data.phone is not None:
            update_fields.append("phone = %s")
            params.append(data.phone)

        if data.avatar_url is not None:
            update_fields.append("avatar_url = %s")
            params.append(data.avatar_url)

        if data.is_active is not None:
            update_fields.append("is_active = %s")
            params.append(data.is_active)

        if data.is_admin is not None:
            update_fields.append("is_admin = %s")
            params.append(data.is_admin)

        if not update_fields:
            raise HTTPException(status_code=400, detail="没有提供要更新的字段")

        # 添加更新时间
        update_fields.append("updated_at = NOW()")
        params.append(user_id)

        # 执行更新
        update_query = f"UPDATE users SET {', '.join(update_fields)} WHERE user_id = %s"
        affected_rows = mysql_manager.execute_update(update_query, tuple(params))

        if affected_rows > 0:
            # 获取更新后的用户信息
            updated_user = mysql_manager.execute_query("""
                SELECT user_id, username, email, display_name, avatar_url, phone,
                       is_active, is_admin, updated_at
                FROM users WHERE user_id = %s
            """, (user_id,))

            user_data = updated_user[0]
            user_data['id'] = user_data['user_id']
            user_data['updated_at'] = user_data['updated_at'].isoformat()

            return {
                "success": True,
                "message": "用户信息更新成功",
                "data": user_data
            }
        else:
            raise HTTPException(status_code=500, detail="更新用户失败")

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新用户失败: {str(e)}")

@router.delete("/admin/users/{user_id}")
def delete_user(
    user_id: str,
    current_user_id: str = Depends(get_current_user_id),
    is_admin: bool = Depends(get_admin_status)
):
    """删除用户（管理端）"""
    try:
        # 验证管理员权限
        verify_admin_permission(is_admin)

        # 防止删除自己
        if current_user_id == user_id:
            raise HTTPException(status_code=400, detail="不能删除自己的账户")

        from ..database.mysql_manager import mysql_manager

        # 检查用户是否存在
        existing_user = mysql_manager.execute_query("""
            SELECT username FROM users WHERE user_id = %s
        """, (user_id,))

        if not existing_user:
            raise HTTPException(status_code=404, detail="用户不存在")

        username = existing_user[0]['username']

        # 检查用户是否有关联数据
        progress_count = mysql_manager.execute_query("""
            SELECT COUNT(*) as count FROM user_video_progress WHERE user_id = %s
        """, (user_id,))

        purchases_count = mysql_manager.execute_query("""
            SELECT COUNT(*) as count FROM user_purchases WHERE user_id = %s
        """, (user_id,))

        total_relations = progress_count[0]['count'] + purchases_count[0]['count']

        if total_relations > 0:
            # 软删除：标记为不活跃而不是物理删除
            mysql_manager.execute_update("""
                UPDATE users SET is_active = 0, updated_at = NOW() WHERE user_id = %s
            """, (user_id,))

            return {
                "success": True,
                "message": f"用户 {username} 已被停用（因为有关联数据）",
                "soft_delete": True
            }
        else:
            # 物理删除
            affected_rows = mysql_manager.execute_update("""
                DELETE FROM users WHERE user_id = %s
            """, (user_id,))

            if affected_rows > 0:
                return {
                    "success": True,
                    "message": f"用户 {username} 已被删除",
                    "soft_delete": False
                }
            else:
                raise HTTPException(status_code=500, detail="删除用户失败")

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除用户失败: {str(e)}")

@router.get("/users/{user_id}/profile")
def get_user_profile(
    user_id: str,
    current_user_id: str = Depends(get_current_user_id),
    is_admin: bool = Depends(get_admin_status)
):
    """获取用户资料"""
    try:
        # 验证访问权限
        verify_user_access_permission(current_user_id, user_id, is_admin)

        from ..database.mysql_manager import mysql_manager

        user = mysql_manager.execute_query("""
            SELECT user_id, username, email, display_name, avatar_url, phone,
                   is_active, last_login_at, created_at
            FROM users WHERE user_id = %s AND is_active = 1
        """, (user_id,))

        if not user:
            raise HTTPException(status_code=404, detail="用户不存在或已被停用")

        user_data = user[0]
        user_data['id'] = user_data['user_id']

        # 转换时间格式
        for field in ['last_login_at', 'created_at']:
            if user_data[field]:
                user_data[field] = user_data[field].isoformat()

        # 获取用户统计信息
        stats = mysql_manager.execute_query("""
            SELECT
                COUNT(DISTINCT uvp.video_id) as watched_videos,
                COUNT(DISTINCT up.series_id) as purchased_series,
                COALESCE(SUM(uvp.watch_count), 0) as total_watch_count
            FROM users u
            LEFT JOIN user_video_progress uvp ON u.user_id = uvp.user_id
            LEFT JOIN user_purchases up ON u.user_id = up.user_id
            WHERE u.user_id = %s
        """, (user_id,))

        if stats:
            user_data['stats'] = stats[0]
        else:
            user_data['stats'] = {
                'watched_videos': 0,
                'purchased_series': 0,
                'total_watch_count': 0
            }

        return {
            'success': True,
            'data': user_data
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取用户资料失败: {str(e)}")

@router.put("/users/{user_id}/profile")
def update_user_profile(
    user_id: str,
    data: UserProfileUpdateRequest,
    current_user_id: str = Depends(get_current_user_id),
    is_admin: bool = Depends(get_admin_status)
):
    """更新用户资料"""
    try:
        # 验证访问权限
        verify_user_access_permission(current_user_id, user_id, is_admin)

        from ..database.mysql_manager import mysql_manager

        # 检查用户是否存在
        existing_user = mysql_manager.execute_query("""
            SELECT user_id FROM users WHERE user_id = %s AND is_active = 1
        """, (user_id,))

        if not existing_user:
            raise HTTPException(status_code=404, detail="用户不存在或已被停用")

        # 构建更新字段
        update_fields = []
        params = []

        if data.display_name is not None:
            update_fields.append("display_name = %s")
            params.append(data.display_name)

        if data.phone is not None:
            update_fields.append("phone = %s")
            params.append(data.phone)

        if data.avatar_url is not None:
            update_fields.append("avatar_url = %s")
            params.append(data.avatar_url)

        if not update_fields:
            raise HTTPException(status_code=400, detail="没有提供要更新的字段")

        # 添加更新时间
        update_fields.append("updated_at = NOW()")
        params.append(user_id)

        # 执行更新
        update_query = f"UPDATE users SET {', '.join(update_fields)} WHERE user_id = %s"
        affected_rows = mysql_manager.execute_update(update_query, tuple(params))

        if affected_rows > 0:
            # 获取更新后的用户资料
            updated_user = mysql_manager.execute_query("""
                SELECT user_id, username, display_name, avatar_url, phone, updated_at
                FROM users WHERE user_id = %s
            """, (user_id,))

            user_data = updated_user[0]
            user_data['id'] = user_data['user_id']
            user_data['updated_at'] = user_data['updated_at'].isoformat()

            return {
                "success": True,
                "message": "用户资料更新成功",
                "data": user_data
            }
        else:
            raise HTTPException(status_code=500, detail="更新用户资料失败")

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新用户资料失败: {str(e)}")
