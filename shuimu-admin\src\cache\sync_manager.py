#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
同步管理器
实现异步三重同步：本地数据库 + 内存缓存 + 服务器同步
"""

import threading
import time
import logging
from typing import Dict, Any, List, Callable
from queue import Queue
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class SyncStatus(Enum):
    """同步状态枚举"""
    PENDING = "pending"      # 待同步
    SYNCING = "syncing"      # 同步中
    SYNCED = "synced"        # 已同步
    FAILED = "failed"        # 同步失败

@dataclass
class SyncTask:
    """同步任务"""
    entity_type: str  # 'series', 'category', 'video'
    entity_id: str
    data: Dict[str, Any]
    attempt_count: int = 0
    max_retries: int = 3

class SyncManager:
    """异步同步管理器"""
    
    def __init__(self):
        self.sync_status: Dict[str, SyncStatus] = {}
        self.retry_queue: Queue = Queue()
        self.manual_sync_queue: List[SyncTask] = []
        self.api_clients = {}
        self.status_callbacks: List[Callable] = []

        # 初始化新的数据同步管理器
        try:
            from sync.data_sync_manager import DataSyncManager
            # 🎯 正确配置：管理端本地数据库作为服务端数据的备份/缓存
            self.data_sync_manager = DataSyncManager(
                local_db_config={
                    'host': 'localhost',
                    'user': 'mike',
                    'password': 'dyj217',
                    'database': 'shuimu_course',  # 管理端本地备份数据库
                    'charset': 'utf8mb4'
                },
                api_base_url='http://localhost:8000'
            )
            print("✅ 新的数据同步管理器初始化成功")
        except Exception as e:
            print(f"⚠️ 新的数据同步管理器初始化失败: {e}")
            self.data_sync_manager = None

        # 启动后台同步线程
        self._start_background_worker()
    
    def set_api_clients(self, series_api, category_api, video_api):
        """设置API客户端"""
        self.api_clients = {
            'series': series_api,
            'category': category_api,
            'video': video_api
        }
    
    def add_status_callback(self, callback: Callable):
        """添加状态变化回调"""
        self.status_callbacks.append(callback)
    
    def _notify_status_change(self, entity_type: str, entity_id: str, status: SyncStatus):
        """通知状态变化"""
        key = f"{entity_type}_{entity_id}"
        self.sync_status[key] = status
        
        # 调用所有回调函数
        for callback in self.status_callbacks:
            try:
                callback(entity_type, entity_id, status)
            except Exception as e:
                logger.error(f"状态回调异常: {e}")
    
    def sync_to_server(self, entity_type: str, entity_id: str, data: Dict[str, Any], operation: str = 'update') -> Dict[str, Any]:
        """同步到服务器（等待响应）- 用于四层同步乐观更新"""
        if not self.data_sync_manager:
            return {
                'success': False,
                'message': '数据同步管理器未初始化'
            }

        try:
            print(f"🌐 同步到服务器: {entity_type} {entity_id} (operation: {operation})")

            # 映射实体类型到表名
            table_name_map = {
                'series': 'series',
                'category': 'categories',
                'categories': 'categories',  # 🎯 关键修复：支持复数形式
                'video': 'videos',
                'videos': 'videos',  # 🎯 关键修复：支持复数形式
                'user': 'users',
                'users': 'users'  # 🎯 关键修复：支持复数形式
            }

            table_name = table_name_map.get(entity_type)
            if not table_name:
                return {
                    'success': False,
                    'message': f'不支持的实体类型: {entity_type}'
                }

            # 🎯 关键修复：使用同步调用，等待服务器响应
            result = self.data_sync_manager.sync_to_server(table_name, entity_id, data, operation)

            # 更新同步状态
            if result.get('success'):
                self._notify_status_change(entity_type, entity_id, SyncStatus.SYNCED)
                print(f"✅ 服务器同步成功: {entity_type} {entity_id}")
            else:
                self._notify_status_change(entity_type, entity_id, SyncStatus.FAILED)
                print(f"❌ 服务器同步失败: {entity_type} {entity_id} - {result.get('message')}")

            return result

        except Exception as e:
            error_msg = f"服务器同步异常: {str(e)}"
            print(f"❌ {error_msg}")
            self._notify_status_change(entity_type, entity_id, SyncStatus.FAILED)
            return {
                'success': False,
                'message': error_msg
            }

    def async_sync_to_server(self, entity_type: str, entity_id: str, data: Dict[str, Any]):
        """异步同步到服务器"""
        task = SyncTask(entity_type, entity_id, data)

        # 标记为同步中
        self._notify_status_change(entity_type, entity_id, SyncStatus.SYNCING)

        # 添加到重试队列
        self.retry_queue.put(task)

        print(f"📤 {entity_type} {entity_id} 已加入异步同步队列")
    
    def async_delete_from_server(self, entity_type: str, entity_id: str):
        """异步删除服务器记录"""
        if not self.data_sync_manager:
            print(f"❌ 数据同步管理器未初始化，无法删除 {entity_type} {entity_id}")
            return
        
        try:
            # 映射实体类型到表名
            table_name_map = {
                'series': 'series',
                'category': 'categories',  # 修正映射
                'video': 'videos'
            }
            table_name = table_name_map.get(entity_type, entity_type)
            
            # 直接从服务器删除，不需要备份
            delete_result = self.data_sync_manager.delete_from_server(table_name, entity_id)
            if delete_result.get('success'):
                print(f"✅ {entity_type} {entity_id} 服务器删除成功")
                
                # 从本地数据库删除
                self._delete_from_local_db(entity_type, entity_id)
                
                self._notify_status_change(entity_type, entity_id, SyncStatus.SYNCED)
            else:
                print(f"❌ {entity_type} {entity_id} 服务器删除失败: {delete_result.get('message')}")
                self._notify_status_change(entity_type, entity_id, SyncStatus.FAILED)
                
        except Exception as e:
            print(f"❌ {entity_type} {entity_id} 删除异常: {e}")
            self._notify_status_change(entity_type, entity_id, SyncStatus.FAILED)
    
    def _create_in_local_db(self, entity_type: str, data: Dict[str, Any]):
        """在本地数据库创建记录"""
        try:
            import pymysql
            
            # 🎯 正确配置：连接管理端本地备份数据库
            conn = pymysql.connect(
                host='localhost',
                user='mike',
                password='dyj217',
                database='shuimu_course',
                charset='utf8mb4'
            )
            
            try:
                cursor = conn.cursor()
                
                if entity_type == 'series':
                    cursor.execute("""
                        INSERT INTO series (id, title, description, price, is_free, is_published, created_at, updated_at)
                        VALUES (%s, %s, %s, %s, %s, %s, NOW(), NOW())
                        ON DUPLICATE KEY UPDATE
                        title=VALUES(title), description=VALUES(description), 
                        price=VALUES(price), is_free=VALUES(is_free), is_published=VALUES(is_published), 
                        updated_at=NOW()
                    """, (
                        data.get('id'),
                        data.get('title', ''),
                        data.get('description', ''),
                        data.get('price', 0.0),
                        data.get('is_free', False),
                        data.get('is_published', True)
                    ))
                elif entity_type == 'category':
                    cursor.execute("""
                        INSERT INTO categories (id, series_id, title, description, price, order_index, created_at, updated_at)
                        VALUES (%s, %s, %s, %s, %s, %s, NOW(), NOW())
                        ON DUPLICATE KEY UPDATE
                        title=VALUES(title), description=VALUES(description), 
                        price=VALUES(price), order_index=VALUES(order_index), updated_at=NOW()
                    """, (
                        data.get('id'),
                        data.get('series_id'),
                        data.get('title', ''),
                        data.get('description', ''),
                        data.get('price', 0.0),
                        data.get('order_index', 0)
                    ))
                elif entity_type == 'video':
                    cursor.execute("""
                        INSERT INTO videos (id, category_id, title, description, video_url, duration, order_index, created_at, updated_at)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
                        ON DUPLICATE KEY UPDATE
                        title=VALUES(title), description=VALUES(description), 
                        video_url=VALUES(video_url), duration=VALUES(duration), 
                        order_index=VALUES(order_index), updated_at=NOW()
                    """, (
                        data.get('id'),
                        data.get('categoryId', data.get('category_id')),  # 🎯 修复：支持服务器字段名
                        data.get('title', ''),
                        data.get('description', ''),
                        data.get('cloudUrl', data.get('video_url', '')),  # 🎯 修复：支持服务器字段名
                        data.get('duration', 0),
                        data.get('order_index', 0)
                    ))
                elif entity_type == 'user':
                    # 用户类型支持（修复关键问题）
                    cursor.execute("""
                        INSERT INTO users (user_id, username, email, password_hash, display_name, phone, avatar_url, is_active, is_admin, created_at, updated_at)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
                        ON DUPLICATE KEY UPDATE
                        username=VALUES(username), email=VALUES(email), display_name=VALUES(display_name),
                        phone=VALUES(phone), avatar_url=VALUES(avatar_url), is_active=VALUES(is_active),
                        is_admin=VALUES(is_admin), updated_at=NOW()
                    """, (
                        data.get('user_id', data.get('id')),  # 支持两种ID字段
                        data.get('username', ''),
                        data.get('email', ''),
                        data.get('password_hash', data.get('password', '')),  # 支持原始密码字段
                        data.get('display_name'),
                        data.get('phone'),
                        data.get('avatar_url', data.get('avatar')),  # 支持两种头像字段名
                        data.get('is_active', True),
                        data.get('is_admin', False)
                    ))
                else:
                    print(f"⚠️ 不支持的实体类型: {entity_type}")
                    return
                
                conn.commit()
                print(f"✅ {entity_type} {data.get('id')} 本地创建成功")
                
            finally:
                conn.close()
                
        except Exception as e:
            print(f"❌ 本地创建失败 {entity_type} {data.get('id')}: {e}")
            raise

    def _update_in_local_db(self, entity_type: str, entity_id: str, data: Dict[str, Any]):
        """在本地数据库更新记录"""
        try:
            import pymysql
            
            # 🎯 正确配置：连接管理端本地备份数据库
            conn = pymysql.connect(
                host='localhost',
                user='mike',
                password='dyj217',
                database='shuimu_course',
                charset='utf8mb4'
            )
            
            try:
                cursor = conn.cursor()
                
                if entity_type == 'series':
                    cursor.execute("""
                        UPDATE series SET 
                        title=%s, description=%s, price=%s, is_free=%s, is_published=%s, updated_at=NOW()
                        WHERE id=%s
                    """, (
                        data.get('title', ''),
                        data.get('description', ''),
                        data.get('price', 0.0),
                        data.get('is_free', False),
                        data.get('is_published', True),
                        entity_id
                    ))
                elif entity_type == 'category':
                    cursor.execute("""
                        UPDATE categories SET 
                        title=%s, description=%s, price=%s, order_index=%s, updated_at=NOW()
                        WHERE id=%s
                    """, (
                        data.get('title', ''),
                        data.get('description', ''),
                        data.get('price', 0.0),
                        data.get('order_index', 0),
                        entity_id
                    ))
                elif entity_type == 'video':
                    cursor.execute("""
                        UPDATE videos SET
                        title=%s, description=%s, video_url=%s, duration=%s, order_index=%s, updated_at=NOW()
                        WHERE id=%s
                    """, (
                        data.get('title', ''),
                        data.get('description', ''),
                        data.get('cloudUrl', data.get('video_url', '')),  # 🎯 修复：支持服务器字段名
                        data.get('duration', 0),
                        data.get('order_index', 0),
                        entity_id
                    ))
                elif entity_type == 'user':
                    # 🎯 修复：添加user类型的更新支持
                    cursor.execute("""
                        UPDATE users SET
                        username=%s, email=%s, display_name=%s, phone=%s, avatar_url=%s,
                        is_active=%s, is_admin=%s, updated_at=NOW()
                        WHERE user_id=%s
                    """, (
                        data.get('username', ''),
                        data.get('email', ''),
                        data.get('display_name'),
                        data.get('phone'),
                        data.get('avatar_url', data.get('avatar')),
                        data.get('is_active', True),
                        data.get('is_admin', False),
                        entity_id
                    ))
                else:
                    print(f"⚠️ 不支持的实体类型: {entity_type}")
                    return
                
                conn.commit()
                print(f"✅ {entity_type} {entity_id} 本地更新成功")
                
            finally:
                conn.close()
                
        except Exception as e:
            print(f"❌ 本地更新失败 {entity_type} {entity_id}: {e}")
            raise

    def _delete_from_local_db(self, entity_type: str, entity_id: str):
        """从本地数据库删除记录"""
        try:
            import pymysql
            
            # 🎯 正确配置：连接管理端本地备份数据库
            conn = pymysql.connect(
                host='localhost',
                user='mike',
                password='dyj217',
                database='shuimu_course',
                charset='utf8mb4'
            )
            
            try:
                cursor = conn.cursor()
                
                table_map = {
                    'series': 'series',
                    'category': 'categories',
                    'video': 'videos',
                    'user': 'users'  # 🎯 修复：添加user类型的删除支持
                }

                if entity_type in table_map:
                    table_name = table_map[entity_type]
                    # 🎯 修复：user类型使用user_id字段，其他类型使用id字段
                    if entity_type == 'user':
                        cursor.execute(f"DELETE FROM {table_name} WHERE user_id = %s", (entity_id,))
                    else:
                        cursor.execute(f"DELETE FROM {table_name} WHERE id = %s", (entity_id,))
                    conn.commit()
                    print(f"✅ {entity_type} {entity_id} 本地删除成功")
                else:
                    print(f"⚠️ 不支持的实体类型: {entity_type}")
                    
            finally:
                conn.close()
                
        except Exception as e:
            print(f"❌ 本地删除失败 {entity_type} {entity_id}: {e}")
            raise
    
    def restore_record(self, entity_type: str, entity_id: str, backup_time: str = None):
        """恢复删除的记录"""
        if not self.data_sync_manager:
            print(f"❌ 数据同步管理器未初始化，无法恢复 {entity_type} {entity_id}")
            return {'success': False, 'message': '同步管理器未初始化'}
        
        try:
            # 映射实体类型到表名
            table_name_map = {
                'series': 'series',
                'category': 'categories',  # 修正映射
                'video': 'videos'
            }
            table_name = table_name_map.get(entity_type, entity_type)
            
            # 从备份恢复到本地
            restore_result = self.data_sync_manager.restore_record(table_name, entity_id, backup_time)
            if restore_result.get('success'):
                print(f"✅ {entity_type} {entity_id} 本地恢复成功")
                
                # 同步到服务器
                restored_data = restore_result.get('data', {})
                self.async_sync_to_server(entity_type, entity_id, restored_data)
                
                return restore_result
            else:
                return restore_result
                
        except Exception as e:
            error_msg = f"恢复异常: {str(e)}"
            print(f"❌ {entity_type} {entity_id} {error_msg}")
            return {'success': False, 'message': error_msg}
    
    def restore_from_backup(self, entity_type: str, entity_id: str, backup_time: str = None):
        """从备份恢复记录（新方法名）"""
        return self.restore_record(entity_type, entity_id, backup_time)
    
    def _start_background_worker(self):
        """启动后台同步工作线程"""
        def worker():
            while True:
                try:
                    # 从队列获取任务（阻塞等待）
                    task = self.retry_queue.get(timeout=1)
                    self._process_sync_task(task)
                    self.retry_queue.task_done()
                except:
                    # 队列为空或超时，继续循环
                    continue
        
        # 启动守护线程
        worker_thread = threading.Thread(target=worker, daemon=True)
        worker_thread.start()
        print("🔄 异步同步后台线程已启动")
    
    def _process_sync_task(self, task: SyncTask):
        """处理同步任务"""
        retry_delays = [1, 3, 5]  # 重试间隔（秒）
        
        while task.attempt_count < task.max_retries:
            task.attempt_count += 1
            
            try:
                print(f"🔄 {task.entity_type} {task.entity_id} 开始同步到服务器（第{task.attempt_count}次尝试）")

                # 优先使用新的数据同步管理器
                if self.data_sync_manager:
                    # 映射实体类型到表名
                    table_name_map = {
                        'series': 'series',
                        'category': 'categories',  # 修正映射
                        'video': 'videos'
                    }
                    table_name = table_name_map.get(task.entity_type, task.entity_type)
                    sync_result = self.data_sync_manager.sync_to_server(table_name, task.entity_id, task.data, 'update')

                    if sync_result.get('success'):
                        print(f"✅ {task.entity_type} {task.entity_id} 服务器同步成功（第{task.attempt_count}次尝试）")
                        self._notify_status_change(task.entity_type, task.entity_id, SyncStatus.SYNCED)
                        return  # 成功，退出重试循环
                    else:
                        error_msg = sync_result.get('message', '未知错误')
                        print(f"⚠️ {task.entity_type} {task.entity_id} 服务器同步失败（第{task.attempt_count}次尝试）: {error_msg}")

                else:
                    # 回退到旧的API客户端
                    api_client = self.api_clients.get(task.entity_type)
                    if not api_client:
                        raise Exception(f"未找到 {task.entity_type} 的API客户端")

                    # 执行API调用
                    if task.entity_type == 'series':
                        api_result = api_client.update_series(task.entity_id, task.data)
                    elif task.entity_type == 'category':
                        api_result = api_client.update_category(task.entity_id, task.data)
                    elif task.entity_type == 'video':
                        api_result = api_client.update_video(task.entity_id, task.data)
                    else:
                        raise Exception(f"不支持的实体类型: {task.entity_type}")

                    # 检查API调用结果
                    if api_result.get('success'):
                        print(f"✅ {task.entity_type} {task.entity_id} 服务器同步成功（第{task.attempt_count}次尝试）")
                        self._notify_status_change(task.entity_type, task.entity_id, SyncStatus.SYNCED)
                        return  # 成功，退出重试循环
                    else:
                        error_msg = api_result.get('message', '未知错误')
                        print(f"⚠️ {task.entity_type} {task.entity_id} 服务器同步失败（第{task.attempt_count}次尝试）: {error_msg}")
                    
            except Exception as e:
                print(f"❌ {task.entity_type} {task.entity_id} 服务器同步异常（第{task.attempt_count}次尝试）: {e}")
            
            # 如果不是最后一次尝试，等待后重试
            if task.attempt_count < task.max_retries:
                delay = retry_delays[min(task.attempt_count - 1, len(retry_delays) - 1)]
                print(f"⏳ {task.entity_type} {task.entity_id} 等待 {delay} 秒后重试...")
                time.sleep(delay)
        
        # 所有重试都失败
        print(f"💀 {task.entity_type} {task.entity_id} 服务器同步最终失败，标记为待手动同步")
        self._notify_status_change(task.entity_type, task.entity_id, SyncStatus.FAILED)
        self.manual_sync_queue.append(task)
    
    def get_sync_status(self, entity_type: str, entity_id: str) -> SyncStatus:
        """获取同步状态"""
        key = f"{entity_type}_{entity_id}"
        return self.sync_status.get(key, SyncStatus.SYNCED)  # 默认为已同步
    
    def get_failed_sync_tasks(self) -> List[SyncTask]:
        """获取失败的同步任务"""
        return self.manual_sync_queue.copy()
    
    def retry_failed_task(self, task: SyncTask):
        """重试失败的任务"""
        task.attempt_count = 0  # 重置尝试次数
        self.manual_sync_queue.remove(task)
        self.async_sync_to_server(task.entity_type, task.entity_id, task.data)
        print(f"🔄 {task.entity_type} {task.entity_id} 已重新加入同步队列")
    
    def clear_failed_tasks(self):
        """清除失败的任务"""
        count = len(self.manual_sync_queue)
        self.manual_sync_queue.clear()
        print(f"🗑️ 已清除 {count} 个失败的同步任务")
    
    def get_sync_statistics(self) -> Dict[str, int]:
        """获取同步统计信息"""
        stats = {
            'pending': 0,
            'syncing': 0,
            'synced': 0,
            'failed': 0
        }
        
        for status in self.sync_status.values():
            stats[status.value] += 1
        
        stats['failed'] = len(self.manual_sync_queue)
        stats['queue_size'] = self.retry_queue.qsize()
        
        return stats

# 全局同步管理器实例
sync_manager = SyncManager()
