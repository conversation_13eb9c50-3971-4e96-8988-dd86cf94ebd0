[database]
# 数据库连接配置 - 统一使用服务端数据库
host = localhost
port = 3306
user = shuimu_server
password = dyj217
database = shuimu_course_server
charset = utf8mb4

[app]
# 应用配置
title = 水幕课程管理端
version = 1.0.0
window_width = 1200
window_height = 800

[ui]
# 界面配置
theme = light
font_size = 12
font_family = Microsoft YaHei

[export]
# 导出配置
default_path = ./exports
date_format = %Y-%m-%d %H:%M:%S

[api]
# API服务配置 - 启用API模式，从服务端加载数据
base_url = http://localhost:8000/api/admin/v1
timeout = 30
use_api = true
retry_count = 3
retry_delay = 1
# 注释：使用API模式从服务端加载数据

[cache]
# 缓存配置
cache_file = ./data/cache.json
cache_expire_hours = 24
auto_sync_interval = 300
enable_offline_mode = false
