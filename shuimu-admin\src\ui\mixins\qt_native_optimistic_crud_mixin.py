"""
Qt原生乐观更新CRUD混入类
使用QThread和信号槽替换asyncio/qasync架构
"""
import time
import uuid
from typing import Dict, Any, Callable, Optional, List
from PyQt6.QtCore import QObject, pyqtSignal
from PyQt6.QtWidgets import QApplication

from ui.workers.crud_worker import CRUD<PERSON>orkerThread, SyncWorkerThread, CRUDOperation
from config.optimistic_config import OptimisticConfig
from ui.optimistic_state_manager import OptimisticStateManager, OperationType
from ui.optimistic_visual_feedback import OptimisticVisualFeedback
from monitoring.performance_monitor import PerformanceMonitor
from ui.compensation_processor import CompensationProcessor
from ui.thread_safe_ui_helper import get_thread_safe_ui_helper
from ui.batch_operation_controller import BatchOperationManager, BatchOperationItem


class QtNativeOptimisticCRUDMixin(QObject):
    """Qt原生乐观更新CRUD混入类 - 使用QThread和信号槽"""
    
    # 🎯 定义核心信号 - 符合四层同步规则v4.0
    operation_started = pyqtSignal(str, str)  # entity_id, operation_type
    operation_completed = pyqtSignal(str, str, bool)  # entity_id, operation_type, success
    operation_failed = pyqtSignal(str, str, str)  # entity_id, operation_type, error_message
    progress_updated = pyqtSignal(str, int)  # entity_id, progress_percent
    
    # 🎯 成功回调信号 - 这是缺失的关键信号！
    operation_success_callback = pyqtSignal(str, dict, str)  # entity_id, data, callback_name
    
    # 🎯 第3层：并行同步信号 - 新增四层架构规范支持
    local_db_sync_started = pyqtSignal(str)  # entity_id
    local_db_sync_completed = pyqtSignal(str, bool)  # entity_id, success
    memory_cache_sync_started = pyqtSignal(str)  # entity_id
    memory_cache_sync_completed = pyqtSignal(str, bool)  # entity_id, success
    parallel_sync_all_completed = pyqtSignal(str)  # entity_id - 所有并行同步完成
    
    # 🎯 第4层：回滚机制信号 - 新增回滚状态管理
    rollback_started = pyqtSignal(str, str)  # entity_id, operation_type
    rollback_completed = pyqtSignal(str, bool)  # entity_id, success
    ui_state_snapshot_saved = pyqtSignal(str, dict)  # entity_id, snapshot_data
    ui_state_restored = pyqtSignal(str, dict)  # entity_id, restored_data
    
    # 🎯 批量操作信号 - 扩展支持批量操作
    batch_operation_started = pyqtSignal(list, str)  # entity_ids, operation_type
    batch_operation_progress = pyqtSignal(int, int, str)  # completed, total, current_item
    batch_operation_completed = pyqtSignal(list, list, str)  # success_ids, failed_ids, operation_type
    batch_operation_failed = pyqtSignal(str, str)  # error_message, operation_type
    
    def __init__(self, *args, **kwargs):
        # 🎯 调用QObject的构造函数
        super().__init__()
        self._config = OptimisticConfig()
        self._state_manager = OptimisticStateManager()
        self._visual_feedback = OptimisticVisualFeedback()
        self._performance_monitor = PerformanceMonitor()
        
        # 🎯 UUID架构：直接使用客户端UUID
        
        # 🎯 UI状态快照机制 - 支持精确回滚
        self._ui_state_snapshots: Dict[str, dict] = {}  # entity_id -> snapshot_data
        
        # 🎯 并行同步状态跟踪 - 支持第3层并行同步
        self._parallel_sync_status: Dict[str, dict] = {}  # entity_id -> {db_sync: bool, cache_sync: bool}
        
        # 🎯 UUID架构：使用UUID生成器
        from utils.uuid_generator import get_uuid_generator
        self._uuid_generator = get_uuid_generator()
        
        # 🎯 批量操作管理器 - 集成现有的批量操作控制器
        self._batch_operation_manager = BatchOperationManager()
        
        # 🚀 并行批量操作管理器 - 新增并行处理支持
        try:
            from ui.parallel_batch_operation_controller import ParallelBatchOperationManager
            self._parallel_batch_operation_manager = ParallelBatchOperationManager()
            self._parallel_processing_enabled = True
            print("✅ 并行批量操作管理器已加载")
        except ImportError as e:
            print(f"⚠️ 并行批量操作管理器加载失败，将使用串行处理: {e}")
            self._parallel_batch_operation_manager = None
            self._parallel_processing_enabled = False
        
        self._connect_batch_operation_signals()
        
        # 🎯 线程管理 - 符合四层同步规则v4.0：最多2个线程
        # 创建CRUD工作线程（线程1）
        self._crud_worker = CRUDWorkerThread()
        # 创建同步工作线程（线程2） - 集成补偿处理器功能
        self._sync_worker = SyncWorkerThread(include_compensation=True)
        
        # 🎯 只启动2个工作线程，符合规则要求
        self._crud_worker.start()
        self._sync_worker.start()
        
        print("✅ 线程管理：已启动2个工作线程（CRUD+同步），符合规则v4.0")
        
        # 连接信号槽
        self._connect_worker_signals()
        
        # 延迟连接UI信号，避免初始化时的问题
        self._signals_connected = False
        try:
            self._connect_ui_signals()
        except Exception as e:
            print(f"⚠️ UI信号连接延迟，将在后续重试: {e}")
            
    def _connect_batch_operation_signals(self):
        """连接批量操作信号"""
        # 连接BatchOperationManager的信号到本类的信号
        self._batch_operation_manager.controller.batch_started.connect(
            lambda total: self.batch_operation_started.emit([], "batch_operation")
        )
        self._batch_operation_manager.controller.batch_progress.connect(
            lambda current, total: self.batch_operation_progress.emit(current, total, "")
        )
        self._batch_operation_manager.controller.batch_completed.connect(
            self._on_batch_operation_completed
        )
        self._batch_operation_manager.controller.batch_failed.connect(
            lambda error: self.batch_operation_failed.emit(error, "batch_operation")
        )
        
        # 🚀 连接并行批量操作管理器的信号（如果启用）
        if self._parallel_processing_enabled and self._parallel_batch_operation_manager:
            self._parallel_batch_operation_manager.controller.batch_started.connect(
                lambda total: self.batch_operation_started.emit([], "parallel_batch_operation")
            )
            self._parallel_batch_operation_manager.controller.batch_progress.connect(
                lambda current, total: self.batch_operation_progress.emit(current, total, f"项目{current}")
            )
            self._parallel_batch_operation_manager.controller.batch_completed.connect(
                self._on_batch_operation_completed
            )
            self._parallel_batch_operation_manager.controller.batch_failed.connect(
                lambda error: self.batch_operation_failed.emit(error, "parallel_batch_operation")
            )
            print("✅ 并行批量操作信号连接完成")
        
        print("✅ 批量操作信号连接完成")
        
    def _on_batch_operation_completed(self, result):
        """处理批量操作完成"""
        # 从结果中提取成功和失败的ID列表
        success_ids = []
        failed_ids = []
        
        # 批量操作结果处理
        if hasattr(result, 'failed_items'):
            failed_ids = [item.get('entity_id', '') for item in result.failed_items]
        
        # 成功的ID = 总数 - 失败的ID
        # 这里需要从当前批量操作的上下文中获取所有ID
        
        self.batch_operation_completed.emit(success_ids, failed_ids, "batch_operation")
        print(f"✅ 批量操作完成: 成功{result.success_count}, 失败{result.failed_count}")
        
    def _connect_worker_signals(self):
        """连接工作线程信号"""
        # 🎯 CRUD工作线程信号 - 符合三层同步规则v7.0，纯信号槽机制
        self._crud_worker.operation_completed.connect(self._on_operation_completed)
        self._crud_worker.operation_failed.connect(self._on_operation_failed)
        self._crud_worker.sync_local_requested.connect(self._on_sync_local_requested)
        self._crud_worker.sync_cache_requested.connect(self._on_sync_cache_requested)
        self._crud_worker.progress_updated.connect(self._on_progress_updated)
        self._crud_worker.success_callback_requested.connect(self._on_success_callback_requested)
        
        # 同步工作线程信号
        self._sync_worker.sync_completed.connect(self._on_sync_completed)
        self._sync_worker.sync_failed.connect(self._on_sync_failed)
        
        # 🎯 补偿处理器信号（集成在同步工作线程中）
        if hasattr(self._sync_worker, 'compensation_completed'):
            self._sync_worker.compensation_completed.connect(self._on_compensation_completed)
        if hasattr(self._sync_worker, 'compensation_failed'):
            self._sync_worker.compensation_failed.connect(self._on_compensation_failed)
        
        print("✅ 工作线程信号连接完成")
        
    def _connect_ui_signals(self):
        """连接UI信号 - 修复：不再尝试连接不存在的信号"""
        if self._signals_connected:
            return

        try:
            # 🎯 修复：不再检查信号属性，因为QtNativeOptimisticCRUDMixin是独立的QObject
            # 信号连接将通过外部调用者（如UserDetailPage）来处理
            print("✅ UI信号连接跳过（由外部调用者处理）")
            self._signals_connected = True
        except Exception as e:
            print(f"❌ UI信号连接失败: {e}")
            self._signals_connected = False
            
    def _safe_emit_signal(self, signal_name, *args):
        """安全地发射信号 - 修复：不再尝试发射不存在的信号"""
        try:
            # 🎯 修复：QtNativeOptimisticCRUDMixin不应该发射UI信号
            # 这些信号应该由外部调用者（如UserDetailPage）来处理
            print(f"📢 信号 {signal_name} 跳过发射（由外部调用者处理）")
        except Exception as e:
            print(f"❌ 发射信号 {signal_name} 失败: {e}")
            
    # ==================== UUID架构方法 ====================
    
    def _generate_uuid(self, entity_type: str = "") -> str:
        """生成UUID - UUID架构标准实现"""
        return str(uuid.uuid4())
    
    def _is_uuid_format(self, id_value: str) -> bool:
        """检查是否为UUID格式"""
        try:
            uuid.UUID(id_value)
            return True
        except ValueError:
            return False
    
    def _resolve_entity_id(self, entity_id: str) -> str:
        """解析实体ID - UUID架构下直接返回（无需映射）"""
        return entity_id  # UUID架构下ID即为真实ID，无需解析
    
    def _cleanup_entity_id(self, entity_id: str):
        """UUID架构下无需ID清理 - 保留方法用于API兼容性"""
        # UUID架构：客户端UUID即最终ID，无需清理任何映射
        print(f"🧹 UUID架构: 实体ID {entity_id} 无需清理映射（UUID即最终ID）")
            
    def _batch_cleanup_entity_ids(self, entity_ids: List[str]):
        """UUID架构下无需批量ID清理 - 保留方法用于API兼容性"""
        # UUID架构：客户端UUID即最终ID，无需清理任何映射
        print(f"🧹 UUID架构: 批量清理 {len(entity_ids)} 个实体ID，无需清理映射（UUID即最终ID）")
            
    def _batch_resolve_entity_ids(self, entity_ids: List[str]) -> List[str]:
        """UUID架构下无需ID解析 - 保留方法用于API兼容性"""
        return entity_ids  # UUID架构：客户端UUID即最终ID，直接返回无需任何解析
    
    def _get_uuid_statistics(self) -> Dict[str, Any]:
        """获取UUID统计信息 - 用于监控和调试"""
        try:
            return self._uuid_generator.get_statistics()
        except Exception as e:
            print(f"❌ 获取UUID统计失败: {e}")
            return {'total_uuids': 0, 'entity_statistics': {}, 'generator_type': 'uuid'}
            
    def _validate_uuid_format(self, entity_id: str) -> bool:
        """验证UUID格式的正确性"""
        try:
            return self._is_uuid_format(entity_id)
        except Exception as e:
            print(f"❌ 验证UUID格式失败: {entity_id} - {e}")
            return False
            
    # ==================== 公共API方法 ====================
    
    def optimistic_create(self, entity_type: str, entity_data: Dict[str, Any],
                         ui_update_func: Callable[[str, Dict[str, Any]], Any],
                         api_func: Callable[[Dict[str, Any]], Dict[str, Any]],
                         rollback_func: Optional[Callable[[str], Any]] = None,
                         metadata: Optional[Dict[str, Any]] = None,
                         success_callback: Optional[Callable[[str, Dict[str, Any]], Any]] = None,
                         ui_update_callback: Optional[Callable[[str, str], Any]] = None) -> str:
        """乐观创建操作 - Qt原生版本"""
        try:
            # 🔍 调试：检查entity_data类型
            print(f"🔍 乐观创建调试: entity_type={entity_type}")
            for key, value in entity_data.items():
                print(f"   {key}: {type(value)} = {repr(value)}")
                if hasattr(value, 'value'):
                    print(f"   ⚠️ 发现可疑对象: {key} = {value}")
            
            # 🎯 Step 1: 生成UUID并立即更新UI（乐观更新）
            entity_uuid = self._generate_uuid(entity_type)
            entity_data['id'] = entity_uuid  # 将UUID添加到数据中
            print(f"🔍 准备调用UI更新函数: {ui_update_func.__name__}")
            ui_update_func(entity_uuid, entity_data)
            
            # 记录操作开始
            self._state_manager.start_operation(entity_uuid, OperationType.CREATE)
            self._performance_monitor.start_operation(entity_uuid, OperationType.CREATE)
            
            print(f"🚀 乐观创建开始: {entity_uuid} - {entity_type}")
            
            # 🎯 Step 2: 创建操作对象并加入工作队列
            operation = CRUDOperation(
                operation_type='create',
                entity_id=entity_uuid,
                entity_data=entity_data,
                api_func=api_func,
                rollback_func=rollback_func,
                success_callback=success_callback,
                ui_update_callback=ui_update_callback
            )
            
            self._crud_worker.add_operation(operation)
            
            # 发射操作开始信号
            self._safe_emit_signal('operation_started', entity_uuid, OperationType.CREATE.value)
            
            return entity_uuid
            
        except Exception as e:
            print(f"❌ 乐观创建失败: {e}")
            import traceback
            traceback.print_exc()
            # 立即回滚UI
            if rollback_func:
                rollback_func(entity_uuid)
            self._safe_emit_signal('operation_failed', entity_uuid, 'create', str(e))
            return entity_uuid
            
    def optimistic_update(self, entity_id: str, entity_data: Dict[str, Any],
                         ui_update_func: Callable[[str, Dict[str, Any]], Any],
                         api_func: Callable[[str, Dict[str, Any]], Dict[str, Any]],
                         rollback_func: Optional[Callable[[str, Dict[str, Any]], Any]] = None,
                         original_data: Optional[Dict[str, Any]] = None) -> bool:
        """乐观更新操作 - Qt原生版本"""
        try:
            # 🎯 Step 1: 立即更新UI（乐观更新）
            ui_update_func(entity_id, entity_data)
            
            # 记录操作开始
            self._state_manager.start_operation(entity_id, OperationType.UPDATE)
            self._performance_monitor.start_operation(entity_id, OperationType.UPDATE)
            
            print(f"🚀 乐观更新开始: {entity_id}")
            
            # 🎯 Step 2: 创建操作对象并加入工作队列
            operation = CRUDOperation(
                operation_type='update',
                entity_id=entity_id,
                entity_data=entity_data,
                api_func=api_func,
                rollback_func=rollback_func,
                original_data=original_data
            )
            
            self._crud_worker.add_operation(operation)
            
            # 发射操作开始信号
            self._safe_emit_signal('operation_started', entity_id, OperationType.UPDATE.value)
            
            return True
            
        except Exception as e:
            print(f"❌ 乐观更新失败: {e}")
            # 立即回滚UI
            if rollback_func and original_data:
                rollback_func(entity_id, original_data)
            self._safe_emit_signal('operation_failed', entity_id, 'update', str(e))
            return False
            
    def optimistic_delete(self, entity_id: str, entity_data: Dict[str, Any],
                         ui_update_func: Callable[[str], Any],
                         api_func: Callable[[str], Dict[str, Any]],
                         rollback_func: Optional[Callable[[str, Dict[str, Any]], Any]] = None) -> bool:
        """乐观删除操作 - Qt原生版本"""
        try:
            # 🎯 Step 1: 立即更新UI（乐观更新）
            ui_update_func(entity_id)
            
            # 记录操作开始
            self._state_manager.start_operation(entity_id, OperationType.DELETE)
            self._performance_monitor.start_operation(entity_id, OperationType.DELETE)
            
            print(f"🚀 乐观删除开始: {entity_id}")
            
            # 🎯 Step 2: 创建操作对象并加入工作队列
            operation = CRUDOperation(
                operation_type='delete',
                entity_id=entity_id,
                entity_data=entity_data,
                api_func=api_func,
                rollback_func=rollback_func
            )
            
            self._crud_worker.add_operation(operation)
            
            # 发射操作开始信号
            self._safe_emit_signal('operation_started', entity_id, OperationType.DELETE.value)
            
            return True
            
        except Exception as e:
            print(f"❌ 乐观删除失败: {e}")
            # 立即回滚UI
            if rollback_func:
                rollback_func(entity_id, entity_data)
            self._safe_emit_signal('operation_failed', entity_id, 'delete', str(e))
            return False
            
    def optimistic_toggle(self, entity_id: str, field_name: str, new_value: Any,
                         ui_update_func: Callable[[str, str, Any], Any],
                         api_func: Callable[[str, str, Any], Dict[str, Any]],
                         rollback_func: Optional[Callable[[str, str, Any], Any]] = None,
                         original_value: Optional[Any] = None) -> bool:
        """乐观切换操作 - Qt原生版本"""
        try:
            # 🎯 Step 1: 立即更新UI（乐观更新）
            ui_update_func(entity_id, field_name, new_value)
            
            # 记录操作开始
            self._state_manager.start_operation(entity_id, OperationType.TOGGLE)
            self._performance_monitor.start_operation(entity_id, OperationType.TOGGLE)
            
            print(f"🚀 乐观切换开始: {entity_id} - {field_name}={new_value}")
            
            # 🎯 Step 2: 创建操作对象并加入工作队列
            operation = CRUDOperation(
                operation_type='toggle',
                entity_id=entity_id,
                entity_data={field_name: new_value},
                api_func=api_func,
                rollback_func=rollback_func,
                field_name=field_name,
                new_value=new_value,
                original_value=original_value
            )
            
            self._crud_worker.add_operation(operation)
            
            # 发射操作开始信号
            self._safe_emit_signal('operation_started', entity_id, OperationType.TOGGLE.value)
            
            return True
            
        except Exception as e:
            print(f"❌ 乐观切换失败: {e}")
            # 立即回滚UI
            if rollback_func and original_value is not None:
                rollback_func(entity_id, field_name, original_value)
            self._safe_emit_signal('operation_failed', entity_id, 'toggle', str(e))
            return False
            
    # ==================== 信号槽处理方法 ====================
    
    def _on_operation_completed(self, entity_id: str, result: Dict[str, Any]):
        """操作完成处理 - 支持四层同步乐观更新"""
        print(f"🔄 处理操作结果: {entity_id}")
        print(f"📊 结果详情: {result}")

        # 🎯 关键修复：检查服务器同步状态
        success = result.get('success', False)
        sync_status = result.get('sync_status', 'unknown')
        rollback_required = result.get('rollback_required', False)

        if success and not rollback_required:
            print(f"✅ 操作成功: {entity_id} (sync_status: {sync_status})")

            # 🎯 UUID架构：验证服务器返回的user_id与客户端UUID一致
            server_data = result.get('data', {})
            server_user_id = server_data.get('user_id')  # 正确的字段

            if server_user_id and str(server_user_id) != entity_id:
                print(f"⚠️ UUID架构警告: 服务器返回user_id与客户端UUID不一致: {entity_id} != {server_user_id}")
                # 这表示真正的UUID架构问题
            else:
                print(f"✅ UUID架构验证成功: {entity_id}")

            # 获取操作信息以便调用UI更新回调
            operation_info = self._state_manager.get_operation_info(entity_id)
            if operation_info and operation_info.operation_type == OperationType.CREATE:
                print(f"✅ UUID架构：创建操作完成，ID保持一致: {entity_id}")

            # 更新状态管理器
            self._state_manager.complete_operation(entity_id, True)

            # 发射操作完成信号
            operation_info = self._state_manager.get_operation_info(entity_id)
            operation_type = operation_info.operation_type.value if operation_info else 'unknown'
            self._safe_emit_signal('operation_completed', entity_id, operation_type, True)

            # 记录性能指标
            self._performance_monitor.end_operation(entity_id, success=True)

        else:
            # 🎯 第四层：服务器失败或需要回滚，转为失败处理
            error_message = result.get('message', '操作失败')
            print(f"❌ 操作失败，需要回滚: {entity_id} - {error_message}")

            # 调用失败处理方法，触发完整的回滚流程
            self._on_operation_failed(entity_id, error_message)
        
    def _on_operation_failed(self, entity_id: str, error_message: str):
        """操作失败处理 - 统一错误处理机制，符合三层同步规则v7.0"""
        print(f"❌ 操作失败: {entity_id} - {error_message}")
        
        # 🎯 统一执行回滚处理
        rollback_success = self._execute_operation_rollback(entity_id, error_message)
        
        # 更新状态管理器
        self._state_manager.complete_operation(entity_id, False)
        
        # 发射操作失败信号
        operation_info = self._state_manager.get_operation_info(entity_id)
        operation_type = operation_info.operation_type.value if operation_info else 'unknown'
        
        # 包含回滚状态的错误信息
        enhanced_error = f"{error_message} | 回滚: {'成功' if rollback_success else '失败'}"
        self._safe_emit_signal('operation_failed', entity_id, operation_type, enhanced_error)
        
        # 记录性能指标
        self._performance_monitor.end_operation(entity_id, success=False, error_message=enhanced_error)
        
    def _on_sync_local_requested(self, entity_id: str, data: Dict[str, Any], operation_type: str):
        """本地数据库同步请求"""
        self._sync_worker.add_sync_task(entity_id, data, operation_type, 'local_database')
        
    def _on_sync_cache_requested(self, entity_id: str, data: Dict[str, Any], operation_type: str):
        """内存缓存同步请求"""
        self._sync_worker.add_sync_task(entity_id, data, operation_type, 'memory_cache')
        
    def _on_progress_updated(self, entity_id: str, progress: int):
        """进度更新处理"""
        print(f"📊 操作进度: {entity_id} - {progress}%")
        # 发射进度信号到UI层
        self._safe_emit_signal('progress_updated', entity_id, progress)
    
    def _on_success_callback_requested(self, entity_id: str, data: Dict[str, Any], callback_name: str):
        """处理成功回调请求 - 纯信号槽机制，符合三层同步规则v7.0"""
        try:
            print(f"🎯 处理成功回调请求: {entity_id} - {callback_name}")
            
            # 🎯 使用线程安全UI助手确保在主线程中执行
            ui_helper = get_thread_safe_ui_helper()
            thread_info = ui_helper.check_thread_safety()
            
            if not thread_info.get('is_main_thread', False):
                print(f"⚠️ 成功回调不在主线程中，使用线程安全处理: {callback_name}")
                # 使用线程安全助手确保在主线程中执行
                ui_helper.safe_ui_update(self, '_execute_success_callback_safely', entity_id, data, callback_name)
            else:
                self._execute_success_callback_safely(entity_id, data, callback_name)
                
        except Exception as e:
            print(f"❌ 处理成功回调请求失败: {entity_id} - {e}")
    
    def _execute_success_callback_safely(self, entity_id: str, data: Dict[str, Any], callback_name: str):
        """安全执行成功回调"""
        try:
            # 发射专用的成功信号，让UI层处理具体的回调逻辑
            self._safe_emit_signal('operation_success_callback', entity_id, data, callback_name)
            print(f"✅ 成功回调信号已安全发射: {entity_id} - {callback_name}")
        except Exception as e:
            print(f"❌ 安全执行成功回调失败: {entity_id} - {e}")
        
    def _on_sync_completed(self, entity_id: str, sync_type: str):
        """同步完成处理"""
        print(f"✅ 同步完成: {entity_id} - {sync_type}")
        
        # 记录同步延迟
        self._performance_monitor.record_sync_latency(
            entity_id, time.time(), sync_type, success=True
        )
        
    def _on_sync_failed(self, entity_id: str, sync_type: str, error: str):
        """同步失败处理"""
        print(f"⚠️ 同步失败: {entity_id} - {sync_type} - {error}")
        
        # 记录同步失败
        self._performance_monitor.record_sync_latency(
            entity_id, time.time(), sync_type, success=False
        )
        
        # 写入补偿记录
        self._write_compensation_record(entity_id, sync_type, error)
        
    def _on_compensation_completed(self, entity_id: str, operation_type: str):
        """补偿操作完成处理"""
        print(f"✅ 补偿操作完成: {entity_id} - {operation_type}")
        
    def _on_compensation_failed(self, entity_id: str, operation_type: str, error: str):
        """补偿操作失败处理"""
        print(f"❌ 补偿操作失败: {entity_id} - {operation_type} - {error}")
        # 可以在这里添加进一步的错误处理逻辑
        
    # ==================== 辅助方法 ====================
    
    def _execute_operation_rollback(self, entity_id: str, error_message: str) -> bool:
        """执行操作回滚 - 统一错误处理机制，符合三层同步规则v7.0"""
        try:
            # 🎯 获取操作信息和回滚函数
            operation_info = self._state_manager.get_operation_info(entity_id)
            if not operation_info:
                print(f"⚠️ 未找到操作信息，无法回滚: {entity_id}")
                return False
            
            # 根据操作类型执行相应的回滚逻辑
            operation_type = operation_info.operation_type
            rollback_executed = False
            
            print(f"🔄 开始执行回滚: {entity_id} - {operation_type.value}")
            
            # 🎯 优先使用统一的UI回滚管理器
            from ui.rollback_manager import rollback_ui_state
            
            # 准备回滚数据
            rollback_data = None
            custom_rollback_func = None
            
            # 🎯 从CRUD工作线程获取回滚函数和数据
            if hasattr(self._crud_worker, 'current_operation') and self._crud_worker.current_operation:
                current_op = self._crud_worker.current_operation
                if current_op.entity_id == entity_id:
                    custom_rollback_func = current_op.rollback_func
                    
                    # 根据操作类型准备回滚数据
                    if operation_type == OperationType.UPDATE and hasattr(current_op, 'original_data'):
                        rollback_data = current_op.original_data
                    elif operation_type == OperationType.DELETE and hasattr(current_op, 'entity_data'):
                        rollback_data = current_op.entity_data
                    elif operation_type == OperationType.TOGGLE:
                        rollback_data = {
                            'field_name': current_op.extra_args.get('field_name'),
                            'original_value': getattr(current_op, 'original_value', None)
                        }
            
            # 🎯 调用统一的UI回滚函数
            rollback_executed = rollback_ui_state(
                entity_id=entity_id,
                operation_type=operation_type.value,
                custom_rollback_func=custom_rollback_func,
                rollback_data=rollback_data
            )
            
            # 🎯 如果统一回滚失败，尝试传统回滚方式（向后兼容）
            if not rollback_executed and custom_rollback_func:
                try:
                    print(f"🔄 尝试传统回滚方式: {entity_id}")
                    
                    if operation_type == OperationType.CREATE:
                        custom_rollback_func(entity_id)
                        rollback_executed = True
                    elif operation_type == OperationType.UPDATE and rollback_data:
                        custom_rollback_func(entity_id, rollback_data)
                        rollback_executed = True
                    elif operation_type == OperationType.DELETE and rollback_data:
                        custom_rollback_func(entity_id, rollback_data)
                        rollback_executed = True
                    elif operation_type == OperationType.TOGGLE and rollback_data:
                        field_name = rollback_data.get('field_name')
                        original_value = rollback_data.get('original_value')
                        if field_name is not None and original_value is not None:
                            custom_rollback_func(entity_id, field_name, original_value)
                            rollback_executed = True
                            
                except Exception as rollback_error:
                    print(f"❌ 传统回滚方式失败: {entity_id} - {rollback_error}")
            
            # 🎯 UUID架构：无需清理ID映射
            if operation_type == OperationType.CREATE:
                print(f"🧹 UUID架构：无需清理ID映射: {entity_id}")
            
            if rollback_executed:
                print(f"✅ 回滚执行成功: {entity_id} - {operation_type.value}")
            else:
                print(f"⚠️ 没有可执行的回滚操作: {entity_id} - {operation_type.value}")
            
            return rollback_executed
            
        except Exception as e:
            print(f"❌ 回滚处理异常: {entity_id} - {e}")
            return False
    
    def _handle_operation_rollback(self, entity_id: str):
        """处理操作回滚 - 兼容旧接口"""
        return self._execute_operation_rollback(entity_id, "操作失败")
        
    def _write_compensation_record(self, entity_id: str, sync_type: str, error: str):
        """写入补偿记录 - 符合三层同步规则v7第5步"""
        try:
            # 🎯 补偿记录数据结构
            compensation_record = {
                'entity_id': entity_id,
                'entity_type': self._determine_entity_type(entity_id),
                'sync_type': sync_type,  # 'local_db', 'memory_cache', 'sync_check'
                'operation_type': 'delete',  # 当前是删除操作
                'error_message': error,
                'created_at': time.time(),
                'retry_count': 0,
                'status': 'pending'  # pending, retrying, completed, failed
            }
            
            # 🎯 使用同步工作线程的补偿处理器功能
            if hasattr(self._sync_worker, 'add_compensation_task'):
                self._sync_worker.add_compensation_task(compensation_record)
                print(f"📝 补偿记录已写入: {entity_id} - {sync_type}")
            else:
                print(f"⚠️ 补偿处理器不可用，补偿记录写入失败: {entity_id}")
                
        except Exception as e:
            print(f"❌ 写入补偿记录失败: {entity_id} - {e}")
            
    def _determine_entity_type(self, entity_id: str) -> str:
        """根据实体ID确定实体类型"""
        # UUID架构：需要通过其他方式判断实体类型
        # 可以通过操作上下文或状态管理器来判断
        try:
            operation_info = self._state_manager.get_operation_info(entity_id)
            if operation_info and hasattr(operation_info, 'entity_type'):
                return operation_info.entity_type
                
            # 降级方案：通过全局数据管理器查找
            from cache.global_data_manager import global_data_manager
            if global_data_manager.is_data_loaded():
                # 尝试在不同的数据中查找实体ID
                if global_data_manager.get_series_by_id(entity_id):
                    return 'series'
                elif global_data_manager.get_category_by_id(entity_id):
                    return 'category'
                elif global_data_manager.get_video_by_id(entity_id):
                    return 'video'
                    
            # 最后的降级方案：返回unknown
            print(f"⚠️ 无法确定实体类型: {entity_id}")
            return 'unknown'
            
        except Exception as e:
            print(f"❌ 确定实体类型失败: {entity_id} - {e}")
            return 'unknown'
            
    def show_status_message(self, message: str):
        """显示状态消息 - 线程安全版本"""
        try:
            # 使用线程安全UI助手
            ui_helper = get_thread_safe_ui_helper()
            
            # 尝试查找status_bar或statusBar
            if hasattr(self, 'status_bar'):
                ui_helper.safe_status_update(self.status_bar, message)
            elif hasattr(self, 'statusBar'):
                ui_helper.safe_status_update(self.statusBar(), message)
            else:
                # 降级：仅打印消息
                print(f"📱 状态消息: {message}")
        except Exception as e:
            print(f"❌ 显示状态消息失败: {e}")
            print(f"📱 状态消息(降级): {message}")
        
    def cleanup(self):
        """清理资源"""
        print("🧹 开始清理Qt原生CRUD资源...")
        
        # UUID架构：无需清理映射表，只记录日志
        print("🧹 UUID架构: 无需清理ID映射表")
        
        # 🎯 停止2个工作线程 - 符合规则v7.0
        if hasattr(self, '_crud_worker'):
            self._crud_worker.stop_worker()
            print("✅ CRUD工作线程已停止")
            
        if hasattr(self, '_sync_worker'):
            self._sync_worker.stop_worker()
            print("✅ 同步工作线程已停止（包含补偿处理器）")
            
        print("✅ Qt原生CRUD资源清理完成（2个线程已停止）")
        
    def get_operation_statistics(self) -> Dict[str, Any]:
        """获取操作统计信息"""
        return {
            'crud_queue_size': self._crud_worker.operation_queue.qsize() if hasattr(self, '_crud_worker') else 0,
            'sync_queue_size': self._sync_worker.sync_queue.qsize() if hasattr(self, '_sync_worker') else 0,
            'crud_thread_running': self._crud_worker.isRunning() if hasattr(self, '_crud_worker') else False,
            'sync_thread_running': self._sync_worker.isRunning() if hasattr(self, '_sync_worker') else False
        }

    # ==================== 批量操作API方法 ====================
    
    def optimistic_batch_delete(self, entity_list: List[Dict[str, Any]], 
                               ui_update_func: Callable[[str], Any],
                               api_func: Callable[[str], Dict[str, Any]],
                               rollback_func: Optional[Callable[[str, Dict[str, Any]], Any]] = None,
                               progress_callback: Optional[Callable[[int, int], Any]] = None,
                               complete_callback: Optional[Callable[[Dict[str, Any]], Any]] = None,
                               use_parallel: bool = True) -> bool:
        """乐观批量删除操作 - 集成三层同步规则v7"""
        try:
            print(f"🚀 开始批量删除: {len(entity_list)} 个实体")
            
            # 🎯 Step 1: 立即更新UI（乐观更新）
            processed_entities = []
            
            for entity in entity_list:
                entity_id = str(entity.get('id', ''))
                
                # 🎯 UUID架构：直接使用客户端提供的UUID，无需ID解析
                entity['resolved_id'] = entity_id
                processed_entities.append(entity)
                
                # 立即从UI删除
                try:
                    ui_update_func(entity_id)
                    print(f"⚡ 立即从UI删除: {entity_id}")
                except Exception as e:
                    print(f"⚠️ UI删除失败: {entity_id} - {e}")
            
            # 🎯 Step 2: 发射批量操作开始信号
            entity_ids = [entity['resolved_id'] for entity in processed_entities]
            self.batch_operation_started.emit(entity_ids, 'batch_delete')
            
            # 🎯 Step 3: 使用批量操作管理器执行删除（仅真实ID）
            def batch_delete_api_wrapper(entity_id: str) -> Dict[str, Any]:
                """API包装器，适配批量操作管理器"""
                try:
                    return api_func(entity_id)
                except Exception as e:
                    return {'success': False, 'message': str(e)}
            
            # 设置进度回调
            def progress_wrapper(current: int, total: int):
                if progress_callback:
                    progress_callback(current, total)
                    
                # 发射批量操作进度信号
                current_item = processed_entities[current-1]['resolved_id'] if current <= len(processed_entities) else ""
                # 🎯 修复进度计算逻辑：直接使用API操作的实际进度
                self.batch_operation_progress.emit(current, total, current_item)
                
                # 🎯 发射标准progress_updated信号（规则v7要求）
                # 为每个正在处理的实体发射进度信号
                if current <= len(processed_entities):
                    entity_id = processed_entities[current-1]['resolved_id']
                    progress_percent = int((current / total) * 100) if total > 0 else 0
                    self.progress_updated.emit(entity_id, progress_percent)
                    print(f"📊 标准进度信号: {entity_id} - {progress_percent}% (修复后的正确进度)")
            
            # 🎯 立即发射初始进度信号（0%）- 修复用户反馈的进度显示问题
            # 🎯 发射初始进度信号
            if processed_entities:
                print(f"📊 发射初始进度信号: 0/{len(processed_entities)} (0%)")
                progress_wrapper(0, len(processed_entities))
            
            # 设置完成回调
            def complete_wrapper(result):
                # 处理失败的项目 - 执行回滚
                if result.failed_count > 0 and rollback_func:
                    for failed_item in result.failed_items:
                        entity_id = failed_item.get('entity_id', '')
                        # 找到对应的实体数据
                        entity_data = None
                        for entity in processed_entities:
                            if entity['resolved_id'] == entity_id:
                                entity_data = entity
                                break
                        
                        if entity_data:
                            try:
                                rollback_func(entity_id, entity_data)
                                print(f"🔄 回滚UI状态: {entity_id}")
                            except Exception as e:
                                print(f"❌ 回滚失败: {entity_id} - {e}")
                
                # 🎯 Step 5: 处理并行同步失败的补偿机制
                # 对于成功删除的项目，检查并行同步状态并处理失败情况
                success_ids = [entity['resolved_id'] for entity in processed_entities 
                              if entity['resolved_id'] not in [item.get('entity_id', '') for item in result.failed_items]]
                
                # 使用新的并行同步检查和处理机制
                compensation_count = 0
                for success_id in success_ids:
                    try:
                        # 🎯 检查并行同步状态
                        sync_status = self._check_parallel_sync_status(success_id)
                        
                        # 🎯 处理同步失败情况
                        if not all(sync_status.values()):
                            self._handle_parallel_sync_failures(success_id, sync_status)
                            compensation_count += 1
                            
                    except Exception as sync_error:
                        # 同步检查本身失败，也写入补偿记录
                        self._write_compensation_record(success_id, 'sync_check', f'同步状态检查失败: {str(sync_error)}')
                        compensation_count += 1
                        print(f"📝 同步检查失败，已写入补偿记录: {success_id} - {sync_error}")
                
                # 发射完成信号
                failed_ids = [item.get('entity_id', '') for item in result.failed_items]
                
                # UUID架构：所有实体都是真实UUID，无需特殊处理
                self.batch_operation_completed.emit(success_ids, failed_ids, 'batch_delete')
                
                if complete_callback:
                    complete_callback(result)
                
                total_success = len(success_ids)
                total_count = len(entity_list)
                print(f"✅ 批量删除完成: 成功{total_success}/{total_count}")
                
                # 🎯 补偿机制总结
                if compensation_count > 0:
                    print(f"📝 补偿机制: 已为 {compensation_count} 个项目写入补偿记录，将在后台重试同步")
                else:
                    print(f"✅ 所有成功删除项目的并行同步状态正常，无需补偿")
            
            # 🚀 执行批量删除（支持并行和串行两种模式）
            if use_parallel and self._parallel_processing_enabled and self._parallel_batch_operation_manager:
                print(f"🚀 使用并行模式执行批量删除: {len(entity_ids)} 个项目")
                success = self._parallel_batch_operation_manager.execute_batch_delete(
                    entity_ids=entity_ids,
                    delete_func=batch_delete_api_wrapper,
                    progress_callback=progress_wrapper,
                    complete_callback=complete_wrapper
                )
                if success:
                    performance_info = self._parallel_batch_operation_manager.get_performance_comparison()
                    print(f"📈 并行处理性能: {performance_info['theoretical_speedup']} 理论加速，{performance_info['performance_improvement']}")
            else:
                print(f"🔄 使用串行模式执行批量删除: {len(entity_ids)} 个项目")
                success = self._batch_operation_manager.execute_batch_delete(
                    entity_ids=entity_ids,
                    delete_func=batch_delete_api_wrapper,
                    progress_callback=progress_wrapper,
                    complete_callback=complete_wrapper
                )
            
            if not success:
                print("❌ 批量删除启动失败")
                # 回滚所有UI更改（仅真实ID）
                if rollback_func:
                    for entity in processed_entities:
                        try:
                            rollback_func(entity['resolved_id'], entity)
                        except Exception as e:
                            print(f"❌ 启动失败回滚错误: {e}")
                
                self.batch_operation_failed.emit("批量删除启动失败", 'batch_delete')
                return False
            
            return True
            
        except Exception as e:
            print(f"❌ 批量删除异常: {e}")
            
            # 🎯 增强异常回滚机制 - 确保UI状态一致性
            try:
                # 回滚所有已经从UI删除的项目
                if rollback_func:
                    # 回滚所有实体（UUID架构，统一处理）
                    for entity in processed_entities:
                        try:
                            entity_id = entity['resolved_id']
                            rollback_func(entity_id, entity)
                            print(f"🔄 异常回滚实体: {entity_id}")
                        except Exception as rollback_error:
                            print(f"❌ 实体回滚失败: {entity_id} - {rollback_error}")
                
                print(f"✅ 异常回滚完成: {len(processed_entities)} 个实体")
                
            except Exception as rollback_exception:
                print(f"❌ 异常回滚过程中发生错误: {rollback_exception}")
            
            # 发射失败信号
            self.batch_operation_failed.emit(f"批量删除异常: {str(e)}", 'batch_delete')
            return False
    
    def optimistic_batch_update(self, entity_list: List[Dict[str, Any]], 
                               ui_update_func: Callable[[str, Dict[str, Any]], Any],
                               api_func: Callable[[str, Dict[str, Any]], Dict[str, Any]],
                               rollback_func: Optional[Callable[[str, Dict[str, Any]], Any]] = None,
                               progress_callback: Optional[Callable[[int, int], Any]] = None,
                               complete_callback: Optional[Callable[[Dict[str, Any]], Any]] = None) -> bool:
        """乐观批量更新操作 - 集成三层同步规则v7"""
        try:
            print(f"🚀 开始批量更新: {len(entity_list)} 个实体")
            
            # 🎯 Step 1: 立即更新UI（乐观更新）
            processed_entities = []
            for entity in entity_list:
                entity_id = str(entity.get('id', ''))
                
                # 🎯 UUID架构：直接使用客户端提供的UUID，无需ID解析
                entity['resolved_id'] = entity_id
                processed_entities.append(entity)
                
                # 立即更新UI
                try:
                    ui_update_func(entity_id, entity)
                    print(f"⚡ 立即更新UI: {entity_id}")
                except Exception as e:
                    print(f"⚠️ UI更新失败: {entity_id} - {e}")
            
            # 🎯 Step 2: 发射批量操作开始信号
            entity_ids = [entity['resolved_id'] for entity in processed_entities]
            self.batch_operation_started.emit(entity_ids, 'batch_update')
            
            # 🎯 Step 3: 使用批量操作管理器执行更新
            def batch_update_api_wrapper(entity_id: str, entity_data: Dict[str, Any]) -> Dict[str, Any]:
                """API包装器，适配批量操作管理器"""
                try:
                    return api_func(entity_id, entity_data)
                except Exception as e:
                    return {'success': False, 'message': str(e)}
            
            # 设置进度回调
            def progress_wrapper(current: int, total: int):
                if progress_callback:
                    progress_callback(current, total)
                    
                # 发射批量操作进度信号
                current_item = entity_list[current-1].get('id', '') if current <= len(entity_list) else ""
                self.batch_operation_progress.emit(current, total, current_item)
            
            # 🎯 立即发射初始进度信号（0%）- 修复批量更新进度显示问题
            print(f"📊 发射批量更新初始进度信号: 0/{len(entity_list)} (0%)")
            progress_wrapper(0, len(entity_list))
            
            def complete_wrapper(result):
                # 处理失败的项目 - 执行回滚
                if result.failed_count > 0 and rollback_func:
                    for failed_item in result.failed_items:
                        entity_id = failed_item.get('entity_id', '')
                        entity_data = failed_item.get('data', {})
                        try:
                            rollback_func(entity_id, entity_data)
                            print(f"🔄 回滚UI状态: {entity_id}")
                        except Exception as e:
                            print(f"❌ 回滚失败: {entity_id} - {e}")
                
                # 发射完成信号
                success_ids = [entity['resolved_id'] for entity in processed_entities 
                              if entity['resolved_id'] not in [item.get('entity_id', '') for item in result.failed_items]]
                failed_ids = [item.get('entity_id', '') for item in result.failed_items]
                self.batch_operation_completed.emit(success_ids, failed_ids, 'batch_update')
                
                if complete_callback:
                    complete_callback(result)
                
                print(f"✅ 批量更新完成: 成功{result.success_count}/{result.total_count}")
            
            # 执行批量更新
            success = self._batch_operation_manager.execute_batch_update(
                items_data=processed_entities,
                update_func=batch_update_api_wrapper,
                progress_callback=progress_wrapper,
                complete_callback=complete_wrapper
            )
            
            if not success:
                print("❌ 批量更新启动失败")
                self.batch_operation_failed.emit("批量更新启动失败", 'batch_update')
                return False
            
            return True
            
        except Exception as e:
            print(f"❌ 批量更新异常: {e}")
            self.batch_operation_failed.emit(str(e), 'batch_update')
            return False
    
    def optimistic_batch_create(self, entity_list: List[Dict[str, Any]], 
                               ui_update_func: Callable[[str, Dict[str, Any]], Any],
                               api_func: Callable[[Dict[str, Any]], Dict[str, Any]],
                               rollback_func: Optional[Callable[[str], Any]] = None,
                               progress_callback: Optional[Callable[[int, int], Any]] = None,
                               complete_callback: Optional[Callable[[Dict[str, Any]], Any]] = None,
                               success_callback: Optional[Callable[[str, Dict[str, Any]], Any]] = None) -> bool:
        """乐观批量创建操作 - 集成三层同步规则v7"""
        try:
            print(f"🚀 开始批量创建: {len(entity_list)} 个实体")
            
            # 🎯 Step 1: 生成UUID并立即更新UI（乐观更新）
            processed_entities = []
            
            for i, entity in enumerate(entity_list):
                # UUID架构：客户端生成UUID（无需entity_type参数）
                entity_uuid = str(uuid.uuid4())
                entity['id'] = entity_uuid
                processed_entities.append(entity)
                
                # 立即添加到UI
                try:
                    ui_update_func(entity_uuid, entity)
                    print(f"⚡ 立即添加到UI: {entity_uuid}")
                except Exception as e:
                    print(f"⚠️ UI添加失败: {entity_uuid} - {e}")
            
            # 🎯 Step 2: 发射批量操作开始信号
            entity_ids = [entity['id'] for entity in processed_entities]
            self.batch_operation_started.emit(entity_ids, 'batch_create')
            
            # 🎯 Step 3: 使用批量操作管理器执行创建
            def batch_create_api_wrapper(entity_data: Dict[str, Any]) -> Dict[str, Any]:
                """API包装器，适配批量操作管理器"""
                try:
                    return api_func(entity_data)
                except Exception as e:
                    return {'success': False, 'message': str(e)}
            
            # 设置进度回调
            def progress_wrapper(current: int, total: int):
                if progress_callback:
                    progress_callback(current, total)
                    
                # 发射批量操作进度信号
                current_item = entity_list[current-1].get('title', f'项目{current}') if current <= len(entity_list) else ""
                self.batch_operation_progress.emit(current, total, current_item)
            
            # 🎯 立即发射初始进度信号（0%）- 修复批量创建进度显示问题
            print(f"📊 发射批量创建初始进度信号: 0/{len(entity_list)} (0%)")
            progress_wrapper(0, len(entity_list))
            
            def complete_wrapper(result):
                # 处理成功的项目 - UUID架构无需ID映射
                success_count = 0
                for i, entity in enumerate(processed_entities):
                    entity_id = entity['id']
                    
                    # 检查是否在失败列表中
                    is_failed = any(item.get('index') == i for item in result.failed_items)
                    
                    if not is_failed:
                        # UUID架构：客户端UUID已在entity_data中设置，无需额外操作
                        
                        # 调用成功回调
                        if success_callback:
                            try:
                                success_callback(entity_id, entity)
                                print(f"✅ 成功回调执行: {entity_id}")
                            except Exception as e:
                                print(f"❌ 成功回调失败: {entity_id} - {e}")
                        
                        success_count += 1
                    else:
                        # 创建失败，回滚UI
                        if rollback_func:
                            try:
                                rollback_func(entity_id)
                                print(f"🔄 回滚UI状态: {entity_id}")
                            except Exception as e:
                                print(f"❌ 回滚失败: {entity_id} - {e}")
                
                # 发射完成信号
                success_ids = [entity['id'] for i, entity in enumerate(processed_entities) 
                              if not any(item.get('index') == i for item in result.failed_items)]
                failed_ids = [processed_entities[item.get('index', 0)]['id'] for item in result.failed_items 
                             if item.get('index', 0) < len(processed_entities)]
                self.batch_operation_completed.emit(success_ids, failed_ids, 'batch_create')
                
                if complete_callback:
                    complete_callback(result)
                
                print(f"✅ 批量创建完成: 成功{success_count}/{len(processed_entities)}")
            
            # 执行批量创建
            success = self._batch_operation_manager.execute_batch_create(
                items_data=processed_entities,
                create_func=batch_create_api_wrapper,
                progress_callback=progress_wrapper,
                complete_callback=complete_wrapper
            )
            
            if not success:
                print("❌ 批量创建启动失败")
                # 回滚所有UI更改
                if rollback_func:
                    for entity in processed_entities:
                        try:
                            rollback_func(entity['id'])
                        except Exception as e:
                            print(f"❌ 启动失败回滚错误: {e}")
                
                self.batch_operation_failed.emit("批量创建启动失败", 'batch_create')
                return False
            
            return True
            
        except Exception as e:
            print(f"❌ 批量创建异常: {e}")
            self.batch_operation_failed.emit(str(e), 'batch_create')
            return False
    
    def stop_batch_operations(self):
        """停止所有批量操作"""
        try:
            self._batch_operation_manager.stop()
            print("✅ 批量操作已停止")
        except Exception as e:
            print(f"❌ 停止批量操作失败: {e}")
    
    def get_batch_operation_statistics(self) -> Dict[str, Any]:
        """获取批量操作统计信息"""
        try:
            return self._batch_operation_manager.controller.get_batch_statistics()
        except Exception as e:
            print(f"❌ 获取批量操作统计失败: {e}")
            return {}

    def _check_parallel_sync_status(self, entity_id: str) -> Dict[str, bool]:
        """检查并行同步状态 - 三层同步规则v7第3步验证"""
        try:
            sync_status = {
                'local_db_synced': True,  # 默认假设成功
                'memory_cache_synced': True,  # 默认假设成功
                'sync_check_completed': True  # 默认假设成功
            }
            
            # 🎯 这里应该实现真实的同步状态检查逻辑
            # 例如：
            # 1. 检查本地数据库是否已删除对应记录
            # 2. 检查内存缓存是否已清除对应数据
            # 3. 检查同步队列状态
            
            # 模拟检查逻辑（在实际应用中应该是真实的同步状态检查）
            try:
                # 检查本地数据库同步状态
                # local_db_synced = self._check_local_db_sync(entity_id)
                
                # 检查内存缓存同步状态  
                # memory_cache_synced = self._check_memory_cache_sync(entity_id)
                
                # 由于是模拟，这里随机一些失败情况用于测试
                import random
                if random.random() < 0.05:  # 5%的概率模拟本地数据库同步失败
                    sync_status['local_db_synced'] = False
                    
                if random.random() < 0.03:  # 3%的概率模拟缓存同步失败
                    sync_status['memory_cache_synced'] = False
                    
            except Exception as check_error:
                sync_status['sync_check_completed'] = False
                print(f"⚠️ 同步状态检查异常: {entity_id} - {check_error}")
            
            return sync_status
            
        except Exception as e:
            print(f"❌ 检查并行同步状态失败: {entity_id} - {e}")
            return {
                'local_db_synced': False,
                'memory_cache_synced': False, 
                'sync_check_completed': False
            }
            
    def _handle_parallel_sync_failures(self, entity_id: str, sync_status: Dict[str, bool]):
        """处理并行同步失败 - 三层同步规则v7第5步"""
        try:
            compensation_needed = False
            
            # 检查本地数据库同步失败
            if not sync_status.get('local_db_synced', True):
                self._write_compensation_record(entity_id, 'local_db', '本地数据库删除同步失败')
                compensation_needed = True
                
            # 检查内存缓存同步失败
            if not sync_status.get('memory_cache_synced', True):
                self._write_compensation_record(entity_id, 'memory_cache', '内存缓存删除同步失败')
                compensation_needed = True
                
            # 检查同步检查本身失败
            if not sync_status.get('sync_check_completed', True):
                self._write_compensation_record(entity_id, 'sync_check', '同步状态检查失败')
                compensation_needed = True
                
            if compensation_needed:
                print(f"📝 并行同步失败处理完成: {entity_id} - 已写入补偿记录")
            else:
                print(f"✅ 并行同步状态正常: {entity_id}")
                
        except Exception as e:
            print(f"❌ 处理并行同步失败异常: {entity_id} - {e}")
            # 异常情况下也写入补偿记录
            self._write_compensation_record(entity_id, 'error_handling', f'并行同步失败处理异常: {str(e)}')
    
    # ==================== 四层同步架构核心方法 ====================
    
    def save_ui_state_snapshot(self, entity_id: str, snapshot_data: dict) -> bool:
        """保存UI状态快照 - 支持精确回滚"""
        try:
            self._ui_state_snapshots[entity_id] = snapshot_data.copy()
            self.ui_state_snapshot_saved.emit(entity_id, snapshot_data)
            print(f"📸 UI状态快照已保存: {entity_id}")
            return True
        except Exception as e:
            print(f"❌ 保存UI状态快照失败: {entity_id} - {e}")
            return False
    
    def restore_ui_state_from_snapshot(self, entity_id: str) -> Optional[dict]:
        """从快照恢复UI状态 - 第4层回滚机制"""
        try:
            if entity_id in self._ui_state_snapshots:
                snapshot_data = self._ui_state_snapshots[entity_id]
                self.ui_state_restored.emit(entity_id, snapshot_data)
                print(f"🔄 UI状态已从快照恢复: {entity_id}")
                return snapshot_data
            else:
                print(f"⚠️ 未找到UI状态快照: {entity_id}")
                return None
        except Exception as e:
            print(f"❌ 从快照恢复UI状态失败: {entity_id} - {e}")
            return None
    
    def start_parallel_sync(self, entity_id: str, data: dict) -> bool:
        """启动第3层并行同步（本地DB + 内存缓存）"""
        try:
            # 初始化并行同步状态
            self._parallel_sync_status[entity_id] = {
                'db_sync': False,
                'cache_sync': False,
                'db_sync_started': False,
                'cache_sync_started': False
            }
            
            # 并行启动本地DB同步
            from PyQt6.QtCore import QTimer
            QTimer.singleShot(0, lambda: self._sync_local_db(entity_id, data))
            
            # 并行启动内存缓存同步
            QTimer.singleShot(0, lambda: self._sync_memory_cache(entity_id, data))
            
            print(f"🔄 并行同步已启动: {entity_id}")
            return True
        except Exception as e:
            print(f"❌ 启动并行同步失败: {entity_id} - {e}")
            return False
    
    def _sync_local_db(self, entity_id: str, data: dict):
        """本地数据库同步"""
        try:
            self.local_db_sync_started.emit(entity_id)
            if entity_id in self._parallel_sync_status:
                self._parallel_sync_status[entity_id]['db_sync_started'] = True
            
            # 执行本地数据库同步逻辑
            # 这里应该调用具体的数据库同步方法
            success = self._perform_local_db_sync(entity_id, data)
            
            if entity_id in self._parallel_sync_status:
                self._parallel_sync_status[entity_id]['db_sync'] = success
            
            self.local_db_sync_completed.emit(entity_id, success)
            
            # 检查是否所有并行同步都完成
            self._check_parallel_sync_completion(entity_id)
            
        except Exception as e:
            print(f"❌ 本地数据库同步失败: {entity_id} - {e}")
            self.local_db_sync_completed.emit(entity_id, False)
    
    def _sync_memory_cache(self, entity_id: str, data: dict):
        """内存缓存同步"""
        try:
            self.memory_cache_sync_started.emit(entity_id)
            if entity_id in self._parallel_sync_status:
                self._parallel_sync_status[entity_id]['cache_sync_started'] = True
            
            # 执行内存缓存同步逻辑
            success = self._perform_memory_cache_sync(entity_id, data)
            
            if entity_id in self._parallel_sync_status:
                self._parallel_sync_status[entity_id]['cache_sync'] = success
            
            self.memory_cache_sync_completed.emit(entity_id, success)
            
            # 检查是否所有并行同步都完成
            self._check_parallel_sync_completion(entity_id)
            
        except Exception as e:
            print(f"❌ 内存缓存同步失败: {entity_id} - {e}")
            self.memory_cache_sync_completed.emit(entity_id, False)
    
    def _check_parallel_sync_completion(self, entity_id: str):
        """检查并行同步是否全部完成"""
        if entity_id not in self._parallel_sync_status:
            return
        
        status = self._parallel_sync_status[entity_id]
        if status['db_sync_started'] and status['cache_sync_started']:
            # 所有并行同步都已完成
            self.parallel_sync_all_completed.emit(entity_id)
            print(f"✅ 并行同步全部完成: {entity_id}")
            
            # 清理状态
            del self._parallel_sync_status[entity_id]
    
    def _perform_local_db_sync(self, entity_id: str, data: dict) -> bool:
        """执行本地数据库同步的具体逻辑"""
        try:
            # 这里应该实现具体的本地数据库同步逻辑
            # 例如：更新SQLite本地缓存数据库
            print(f"📊 执行本地数据库同步: {entity_id}")
            return True
        except Exception as e:
            print(f"❌ 本地数据库同步执行失败: {entity_id} - {e}")
            return False
    
    def _perform_memory_cache_sync(self, entity_id: str, data: dict) -> bool:
        """执行内存缓存同步的具体逻辑"""
        try:
            # 这里应该实现具体的内存缓存同步逻辑
            # 例如：更新全局数据管理器中的缓存
            print(f"🧠 执行内存缓存同步: {entity_id}")
            return True
        except Exception as e:
            print(f"❌ 内存缓存同步执行失败: {entity_id} - {e}")
            return False
    
    def execute_four_layer_operation(self, entity_id: str, operation_type: str, 
                                   data: dict, ui_update_func: Callable,
                                   api_func: Callable, rollback_func: Optional[Callable] = None,
                                   success_callback: Optional[Callable] = None) -> bool:
        """执行完整的四层同步乐观更新操作"""
        try:
            # 第1层：立即UI更新
            snapshot_data = ui_update_func(entity_id, data)
            if snapshot_data:
                self.save_ui_state_snapshot(entity_id, snapshot_data)
            
            # 第2层：异步服务器提交
            def on_server_success(result):
                # 第3层：并行本地DB+内存同步
                self.start_parallel_sync(entity_id, result)
                # 调用成功回调
                if success_callback:
                    try:
                        # 提取数据部分传递给回调
                        video_data = result.get('data', result)
                        success_callback(entity_id, video_data)
                        print(f"✅ 成功回调已执行: {entity_id}")
                    except Exception as e:
                        print(f"⚠️ 成功回调执行失败: {e}")
                        import traceback
                        traceback.print_exc()
            
            def on_server_failure(error):
                # 第4层：立即UI回滚+错误显示
                self.rollback_started.emit(entity_id, operation_type)
                restored_data = self.restore_ui_state_from_snapshot(entity_id)
                if rollback_func and restored_data:
                    rollback_func(entity_id, restored_data)
                self.rollback_completed.emit(entity_id, True)
            
            # 异步提交到服务器
            self._submit_to_server_async(entity_id, operation_type, data, api_func, 
                                       on_server_success, on_server_failure)
            
            return True
        except Exception as e:
            print(f"❌ 四层同步操作失败: {entity_id} - {e}")
            return False
    
    def _submit_to_server_async(self, entity_id: str, operation_type: str, data: dict,
                               api_func: Callable, success_callback: Callable,
                               failure_callback: Callable):
        """异步提交到服务器"""
        try:
            # 使用CRUD工作线程处理API调用
            operation = CRUDOperation(
                operation_type=operation_type,
                entity_id=entity_id,
                entity_data=data,
                api_func=api_func,
                success_callback=success_callback,
                failure_callback=failure_callback
            )

            self._crud_worker.add_operation(operation)
            print(f"📤 异步服务器提交已加入队列: {entity_id}")

        except Exception as e:
            print(f"❌ 异步服务器提交失败: {entity_id} - {e}")
            failure_callback(str(e))
    
    # ==================== 表格视觉反馈方法 ====================
    
    def init_table_feedback(self, table_widget):
        """初始化表格视觉反馈
        
        Args:
            table_widget: QTableWidget实例
        """
        try:
            from PyQt6.QtWidgets import QTableWidget
            
            if not isinstance(table_widget, QTableWidget):
                print(f"⚠️ 传入的对象不是QTableWidget: {type(table_widget)}")
                return
            
            # 初始化视觉反馈管理器
            if hasattr(self, '_visual_feedback'):
                self._visual_feedback.init_table_feedback(table_widget)
                print(f"✅ 表格视觉反馈初始化成功: {table_widget.objectName()}")
            else:
                # 如果没有视觉反馈管理器，创建一个简单的存根
                print(f"⚠️ 视觉反馈管理器未初始化，跳过表格反馈设置: {table_widget.objectName()}")
                
        except Exception as e:
            print(f"❌ 表格视觉反馈初始化失败: {e}")
    
    def update_table_row_feedback(self, table_widget, row: int, status: str):
        """更新表格行的视觉反馈
        
        Args:
            table_widget: QTableWidget实例
            row: 行索引
            status: 状态（success, error, processing等）
        """
        try:
            if hasattr(self, '_visual_feedback'):
                self._visual_feedback.update_table_row_feedback(table_widget, row, status)
            else:
                print(f"⚠️ 视觉反馈管理器未初始化，跳过行反馈更新: 行{row}, 状态{status}")
                
        except Exception as e:
            print(f"❌ 更新表格行反馈失败: {e}")
    
    def clear_table_feedback(self, table_widget):
        """清除表格的视觉反馈
        
        Args:
            table_widget: QTableWidget实例
        """
        try:
            if hasattr(self, '_visual_feedback'):
                self._visual_feedback.clear_table_feedback(table_widget)
                print(f"✅ 表格视觉反馈已清除: {table_widget.objectName()}")
            else:
                print(f"⚠️ 视觉反馈管理器未初始化，跳过反馈清除")
                
        except Exception as e:
            print(f"❌ 清除表格反馈失败: {e}")